<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>30天备孕营养菜谱计划 - 健康备孕指南</title>
    <meta name="description" content="专业的30天备孕营养菜谱计划，科学搭配，营养均衡，助力健康备孕">
    <meta name="keywords" content="备孕,营养,菜谱,健康,叶酸,蛋白质,维生素">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 40px 20px;
            border-radius: 20px;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .header p {
            font-size: 1.2rem;
            opacity: 0.9;
        }

        .navigation {
            background: white;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 30px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            position: sticky;
            top: 20px;
            z-index: 100;
        }

        .nav-buttons {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            justify-content: center;
        }

        .nav-btn {
            background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 25px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 15px;
            font-weight: 500;
            min-width: 120px;
        }

        .nav-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(255,154,158,0.4);
        }

        .nav-btn.active {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        .content-section {
            background: white;
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            display: none;
        }

        .content-section.active {
            display: block;
        }

        .nutrition-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .nutrition-card {
            background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
            padding: 20px;
            border-radius: 15px;
            text-align: center;
            transition: transform 0.3s ease;
        }

        .nutrition-card:hover {
            transform: translateY(-5px);
        }

        .nutrition-card h3 {
            color: #333;
            margin-bottom: 10px;
            font-size: 1.3rem;
        }

        .nutrition-card p {
            color: #666;
            font-size: 0.95rem;
        }

        .day-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .day-card {
            background: white;
            border: 2px solid #f0f0f0;
            border-radius: 15px;
            overflow: hidden;
            transition: all 0.3s ease;
            cursor: pointer;
            height: fit-content;
        }

        .day-card:hover {
            border-color: #667eea;
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(102,126,234,0.15);
        }

        .day-card-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px 20px;
            text-align: center;
            position: relative;
        }

        .day-card-header::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 2px;
            background: linear-gradient(90deg, #ff6b6b, #feca57, #48dbfb, #ff9ff3);
        }

        .day-card h3 {
            font-size: 1.3rem;
            margin: 0;
            font-weight: 600;
        }

        .day-card-content {
            padding: 16px;
        }

        .meal-item {
            margin-bottom: 12px;
            padding: 12px;
            background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
            border-radius: 10px;
            border-left: 3px solid #ff9a9e;
            transition: all 0.2s ease;
        }

        .meal-item:hover {
            background: linear-gradient(135deg, #f0f4ff 0%, #ffffff 100%);
            border-left-color: #667eea;
        }

        .meal-item:last-child {
            margin-bottom: 0;
        }

        .meal-header {
            display: flex;
            align-items: center;
            margin-bottom: 6px;
        }

        .meal-icon {
            font-size: 1.1rem;
            margin-right: 6px;
        }

        .meal-title {
            font-weight: 600;
            color: #333;
            font-size: 0.9rem;
        }

        .meal-content {
            color: #666;
            font-size: 0.85rem;
            line-height: 1.3;
            margin-bottom: 8px;
        }

        .meal-image {
            width: 100%;
            height: 80px;
            object-fit: cover;
            border-radius: 6px;
            transition: opacity 0.3s ease;
        }

        .meal-image.loading {
            opacity: 0.7;
            background: linear-gradient(45deg, #f0f0f0 25%, transparent 25%),
                        linear-gradient(-45deg, #f0f0f0 25%, transparent 25%),
                        linear-gradient(45deg, transparent 75%, #f0f0f0 75%),
                        linear-gradient(-45deg, transparent 75%, #f0f0f0 75%);
            background-size: 20px 20px;
            background-position: 0 0, 0 10px, 10px -10px, -10px 0px;
        }

        .meal-image.error {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            color: #6c757d;
            font-size: 1.5rem;
        }

        /* 新增：快速预览功能 */
        .day-card-quick-info {
            position: absolute;
            top: 10px;
            right: 10px;
            background: rgba(255, 255, 255, 0.9);
            border-radius: 20px;
            padding: 4px 8px;
            font-size: 0.7rem;
            color: #667eea;
            font-weight: 600;
        }

        /* 新增：营养标签 */
        .nutrition-highlights {
            display: flex;
            flex-wrap: wrap;
            gap: 4px;
            margin-top: 8px;
        }

        .nutrition-tag-small {
            background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
            color: white;
            padding: 2px 6px;
            border-radius: 10px;
            font-size: 0.7rem;
            font-weight: 500;
        }

        /* 新增：卡片网格布局优化 */
        .day-grid-container {
            position: relative;
        }

        .grid-view-toggle {
            display: flex;
            justify-content: center;
            margin-bottom: 20px;
            gap: 10px;
        }

        .view-btn {
            background: #f8f9fa;
            border: 2px solid #e9ecef;
            color: #666;
            padding: 8px 16px;
            border-radius: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 0.9rem;
        }

        .view-btn.active {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-color: #667eea;
            color: white;
        }

        .view-btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 3px 10px rgba(0,0,0,0.1);
        }

        /* 周视图特殊样式 */
        .week-container {
            background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 25px;
            box-shadow: 0 3px 15px rgba(0,0,0,0.05);
        }

        .week-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }

        .week-day-card {
            background: white;
            border: 1px solid #e9ecef;
            border-radius: 10px;
            padding: 15px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .week-day-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            border-color: #667eea;
        }

        /* 紧凑视图优化 */
        .day-grid.compact-view {
            grid-template-columns: repeat(auto-fill, minmax(260px, 1fr));
            gap: 16px;
        }

        .day-grid.detailed-view {
            grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
            gap: 20px;
        }

        /* 营养标签动画 */
        .nutrition-tag-small {
            animation: fadeInUp 0.3s ease-out;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* 卡片加载动画 */
        .day-card {
            animation: slideInUp 0.4s ease-out;
        }

        @keyframes slideInUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* 响应式优化 */
        @media (max-width: 576px) {
            .grid-view-toggle {
                flex-direction: column;
                align-items: center;
            }

            .view-btn {
                width: 200px;
                text-align: center;
            }

            .week-grid {
                grid-template-columns: 1fr;
            }
        }

        .recipe-detail {
            background: white;
            border-radius: 15px;
            padding: 30px;
            margin-top: 20px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            display: none;
        }

        .recipe-detail.active {
            display: block;
        }

        .recipe-header {
            text-align: center;
            margin-bottom: 30px;
        }

        .recipe-header h2 {
            color: #667eea;
            font-size: 2rem;
            margin-bottom: 10px;
        }

        .meal-section {
            margin-bottom: 40px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 12px;
        }

        .meal-section h3 {
            color: #333;
            font-size: 1.5rem;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 2px solid #667eea;
        }

        .dish-item {
            margin-bottom: 40px;
            background: white;
            border-radius: 20px;
            overflow: hidden;
            box-shadow: 0 8px 25px rgba(0,0,0,0.12);
            border: 1px solid #f0f0f0;
            transition: all 0.3s ease;
        }

        .dish-item:hover {
            transform: translateY(-3px);
            box-shadow: 0 12px 35px rgba(0,0,0,0.15);
        }

        .dish-header {
            display: flex;
            align-items: center;
            padding: 25px;
            background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 50%, #fad0c4 100%);
            position: relative;
        }

        .dish-header::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, #ff6b6b, #feca57, #48dbfb, #ff9ff3);
        }

        .dish-image {
            width: 100px;
            height: 100px;
            object-fit: cover;
            border-radius: 15px;
            margin-right: 20px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.3);
            border: 3px solid white;
        }

        .dish-title-container {
            flex: 1;
        }

        .dish-title {
            font-weight: bold;
            color: white;
            font-size: 1.5rem;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
            margin-bottom: 8px;
        }

        .dish-nutrition-tags {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            margin-top: 10px;
        }

        .nutrition-tag {
            background: rgba(255,255,255,0.9);
            color: #333;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 500;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }

        .dish-content {
            padding: 30px;
        }

        .content-section-dish {
            margin-bottom: 25px;
            padding: 20px;
            border-radius: 15px;
            border-left: 4px solid;
        }

        .ingredients {
            background: linear-gradient(135deg, #e8f5e8 0%, #f0f8f0 100%);
            border-left-color: #28a745;
        }

        .ingredients strong {
            color: #28a745;
            display: flex;
            align-items: center;
            margin-bottom: 12px;
            font-size: 1.1rem;
        }

        .ingredients-list {
            color: #555;
            line-height: 1.6;
            font-size: 1rem;
        }

        .steps {
            background: linear-gradient(135deg, #e8f0ff 0%, #f0f6ff 100%);
            border-left-color: #667eea;
        }

        .steps strong {
            color: #667eea;
            display: flex;
            align-items: center;
            margin-bottom: 15px;
            font-size: 1.1rem;
        }

        .steps ol {
            padding-left: 25px;
            color: #555;
        }

        .steps li {
            margin-bottom: 12px;
            line-height: 1.6;
            padding: 8px 0;
            border-bottom: 1px solid #e9ecef;
        }

        .steps li:last-child {
            border-bottom: none;
        }

        .step-time {
            background: #667eea;
            color: white;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 0.75rem;
            margin-left: 10px;
            font-weight: 500;
        }

        .tips {
            background: linear-gradient(135deg, #fff3cd 0%, #fef9e7 100%);
            border-left-color: #ffc107;
        }

        .tips strong {
            color: #856404;
            display: flex;
            align-items: center;
            margin-bottom: 10px;
            font-size: 1.1rem;
        }

        .tips-content {
            color: #856404;
            line-height: 1.6;
            font-size: 0.95rem;
        }

        .cooking-time {
            background: linear-gradient(135deg, #e1f5fe 0%, #f0f9ff 100%);
            border-left-color: #03a9f4;
            text-align: center;
        }

        .cooking-time strong {
            color: #0277bd;
            justify-content: center;
            font-size: 1.1rem;
        }

        .time-info {
            display: flex;
            justify-content: space-around;
            margin-top: 10px;
        }

        .time-item {
            text-align: center;
        }

        .time-value {
            display: block;
            font-size: 1.5rem;
            font-weight: bold;
            color: #0277bd;
        }

        .time-label {
            font-size: 0.9rem;
            color: #666;
        }

        .back-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 25px;
            cursor: pointer;
            margin-bottom: 20px;
            transition: all 0.3s ease;
        }

        .back-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102,126,234,0.4);
        }

        /* 桌面端优化 - 大屏幕显示更多列 */
        @media (min-width: 1400px) {
            .day-grid {
                grid-template-columns: repeat(auto-fill, minmax(260px, 1fr));
                gap: 18px;
            }

            .container {
                max-width: 1400px;
            }
        }

        @media (min-width: 1200px) and (max-width: 1399px) {
            .day-grid {
                grid-template-columns: repeat(4, 1fr);
            }
        }

        @media (min-width: 992px) and (max-width: 1199px) {
            .day-grid {
                grid-template-columns: repeat(3, 1fr);
            }
        }

        @media (min-width: 768px) and (max-width: 991px) {
            .day-grid {
                grid-template-columns: repeat(2, 1fr);
                gap: 16px;
            }

            .day-card-content {
                padding: 14px;
            }

            .meal-item {
                padding: 10px;
            }
        }

        @media (max-width: 767px) {
            .container {
                padding: 10px;
            }

            .header h1 {
                font-size: 2rem;
            }

            .nav-buttons {
                justify-content: center;
            }

            .nav-btn {
                font-size: 12px;
                padding: 8px 15px;
                min-width: 100px;
            }

            .day-grid {
                grid-template-columns: 1fr;
                gap: 15px;
            }

            .nutrition-grid {
                grid-template-columns: 1fr;
            }

            .dish-header {
                flex-direction: column;
                text-align: center;
            }

            .dish-image {
                margin-right: 0;
                margin-bottom: 10px;
            }

            .meal-image {
                height: 60px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🌟 30天备孕营养菜谱计划</h1>
            <p>科学搭配 · 营养均衡 · 助力健康备孕</p>
        </div>

        <div class="navigation">
            <div class="nav-buttons">
                <button class="nav-btn active" onclick="showSection('guide')">📋 营养指南</button>
                <button class="nav-btn" onclick="showSection('all-days')">📅 完整菜谱</button>
                <button class="nav-btn" onclick="showSection('summary')">📊 计划总结</button>
            </div>
        </div>

        <!-- 营养指南部分 -->
        <div id="guide" class="content-section active">
            <h2 style="text-align: center; color: #667eea; margin-bottom: 30px;">📋 营养指南与使用说明</h2>

            <div class="nutrition-grid">
                <div class="nutrition-card">
                    <h3>🥬 叶酸</h3>
                    <p>每日400微克<br>主要来源：菠菜、韭菜等绿叶菜</p>
                </div>
                <div class="nutrition-card">
                    <h3>🩸 铁质</h3>
                    <p>预防贫血<br>来源：瘦肉、菠菜、红枣</p>
                </div>
                <div class="nutrition-card">
                    <h3>⚡ 锌</h3>
                    <p>提高生育能力<br>来源：虾仁、瘦肉、坚果</p>
                </div>
                <div class="nutrition-card">
                    <h3>🌰 维生素E</h3>
                    <p>抗氧化<br>来源：坚果、植物油</p>
                </div>
                <div class="nutrition-card">
                    <h3>🦴 钙质</h3>
                    <p>骨骼发育<br>来源：豆腐、虾皮、芝麻</p>
                </div>
                <div class="nutrition-card">
                    <h3>🥚 优质蛋白</h3>
                    <p>每餐必备<br>来源：鸡蛋、肉类、豆制品</p>
                </div>
            </div>

            <div style="background: #f8f9fa; padding: 25px; border-radius: 15px; margin-top: 30px;">
                <h3 style="color: #333; margin-bottom: 20px;">🥚 鸡蛋摄入量说明</h3>
                <ul style="color: #666; line-height: 1.8;">
                    <li><strong>科学标准：</strong>每天1-2个鸡蛋为宜，本计划控制在每天平均1.5个</li>
                    <li><strong>分配原则：</strong>早餐1个，午餐或晚餐0.5-1个，避免过量</li>
                    <li><strong>营养保证：</strong>通过多样化蛋白质来源确保营养充足</li>
                </ul>
            </div>

            <div style="background: #e8f4fd; padding: 25px; border-radius: 15px; margin-top: 20px;">
                <h3 style="color: #333; margin-bottom: 20px;">🍳 烹饪要点</h3>
                <ul style="color: #666; line-height: 1.8;">
                    <li>详细步骤说明，包含时间和火候</li>
                    <li>提供关键技巧提示</li>
                    <li>适合烹饪新手操作</li>
                    <li>优先选择普通、易购买的食材</li>
                </ul>
            </div>
        </div>

        <!-- 完整菜谱 -->
        <div id="all-days" class="content-section">
            <h2 style="text-align: center; color: #667eea; margin-bottom: 20px;">📅 30天完整菜谱计划</h2>

            <!-- 视图切换按钮 -->
            <div class="grid-view-toggle">
                <button class="view-btn active" onclick="switchView('compact')">🔲 紧凑视图</button>
                <button class="view-btn" onclick="switchView('detailed')">📋 详细视图</button>
                <button class="view-btn" onclick="switchView('weekly')">📅 周视图</button>
            </div>

            <div class="day-grid-container">
                <div class="day-grid" id="all-days-grid">
                    <!-- 这里将通过JavaScript动态生成所有30天的内容 -->
                </div>
            </div>
        </div>

        <!-- 计划总结 -->
        <div id="summary" class="content-section">
            <h2 style="text-align: center; color: #667eea; margin-bottom: 30px;">📊 30天计划总结</h2>

            <div class="nutrition-grid">
                <div class="nutrition-card">
                    <h3>🥬 叶酸充足</h3>
                    <p>每天都有绿叶菜（菠菜、韭菜等）</p>
                </div>
                <div class="nutrition-card">
                    <h3>🥩 蛋白质丰富</h3>
                    <p>每餐都有鸡蛋、肉类或豆制品</p>
                </div>
                <div class="nutrition-card">
                    <h3>🩸 铁质补充</h3>
                    <p>瘦肉、菠菜、红枣等铁质丰富食物</p>
                </div>
                <div class="nutrition-card">
                    <h3>🦴 钙质保证</h3>
                    <p>豆腐、虾皮、芝麻等钙质来源</p>
                </div>
                <div class="nutrition-card">
                    <h3>🌈 维生素全面</h3>
                    <p>各色蔬菜水果，营养均衡</p>
                </div>
                <div class="nutrition-card">
                    <h3>🥚 鸡蛋用量科学</h3>
                    <p>每天平均1.5个鸡蛋，符合国际营养标准</p>
                </div>
            </div>

            <div style="background: #f8f9fa; padding: 25px; border-radius: 15px; margin-top: 30px;">
                <h3 style="color: #333; margin-bottom: 20px;">💡 使用建议</h3>
                <ol style="color: #666; line-height: 1.8;">
                    <li><strong>循序渐进：</strong>前几天选择简单菜品熟悉</li>
                    <li><strong>灵活调整：</strong>可根据个人喜好适当替换</li>
                    <li><strong>营养均衡：</strong>确保每日营养素摄入充足</li>
                    <li><strong>坚持执行：</strong>连续30天形成良好饮食习惯</li>
                </ol>
            </div>
        </div>

        <!-- 菜谱详情页面 -->
        <div id="recipe-detail" class="recipe-detail">
            <button class="back-btn" onclick="hideRecipeDetail()">← 返回菜谱列表</button>
            <div id="recipe-content">
                <!-- 详细菜谱内容将在这里显示 -->
            </div>
        </div>
    </div>

    <script>
        // 美食图片映射 - 使用高质量外部图片
        const foodImages = {
            '营养蒸蛋': 'https://images.unsplash.com/photo-1506084868230-bb9d95c24759?w=400&h=300&fit=crop&auto=format',
            '小米粥': 'https://images.unsplash.com/photo-1547592180-85f173990554?w=400&h=300&fit=crop&auto=format',
            '番茄鸡蛋面': 'https://images.unsplash.com/photo-1555126634-323283e090fa?w=400&h=300&fit=crop&auto=format',
            '紫菜蛋花汤': 'https://images.unsplash.com/photo-1547592166-23ac45744acd?w=400&h=300&fit=crop&auto=format',
            '凉拌黄瓜': 'https://images.unsplash.com/photo-1512621776951-a57141f2eefd?w=400&h=300&fit=crop&auto=format',
            '豆浆': 'https://images.unsplash.com/photo-1544787219-7f47ccb76574?w=400&h=300&fit=crop&auto=format',
            '菠菜鸡蛋饼': 'https://images.unsplash.com/photo-1506084868230-bb9d95c24759?w=400&h=300&fit=crop&auto=format',
            '肉末蒸蛋羹': 'https://images.unsplash.com/photo-1506084868230-bb9d95c24759?w=400&h=300&fit=crop&auto=format',
            '菠菜汤': 'https://images.unsplash.com/photo-1547592180-85f173990554?w=400&h=300&fit=crop&auto=format',
            '虾仁蒸蛋': 'https://images.unsplash.com/photo-1565299624946-b28f40a0ca4b?w=400&h=300&fit=crop&auto=format',
            '凉拌豆腐丝': 'https://images.unsplash.com/photo-1571167530149-c72f2b3d0fdc?w=400&h=300&fit=crop&auto=format',
            '核桃粥': 'https://images.unsplash.com/photo-1547592180-85f173990554?w=400&h=300&fit=crop&auto=format',
            '水煮蛋': 'https://images.unsplash.com/photo-1506084868230-bb9d95c24759?w=400&h=300&fit=crop&auto=format',
            '炒胡萝卜丝': 'https://images.unsplash.com/photo-1445282768818-728615cc910a?w=400&h=300&fit=crop&auto=format',
            '小米饭': 'https://images.unsplash.com/photo-1586201375761-83865001e31c?w=400&h=300&fit=crop&auto=format',
            '冬瓜蛋汤': 'https://images.unsplash.com/photo-1547592166-23ac45744acd?w=400&h=300&fit=crop&auto=format',
            '拌菠菜': 'https://images.unsplash.com/photo-1512621776951-a57141f2eefd?w=400&h=300&fit=crop&auto=format',
            '红豆粥': 'https://images.unsplash.com/photo-1547592180-85f173990554?w=400&h=300&fit=crop&auto=format',
            '韭菜炒鸡蛋': 'https://images.unsplash.com/photo-1506084868230-bb9d95c24759?w=400&h=300&fit=crop&auto=format',
            '鸡蛋汤': 'https://images.unsplash.com/photo-1547592166-23ac45744acd?w=400&h=300&fit=crop&auto=format',
            '炒青菜': 'https://images.unsplash.com/photo-1512621776951-a57141f2eefd?w=400&h=300&fit=crop&auto=format',
            '红豆饭': 'https://images.unsplash.com/photo-1586201375761-83865001e31c?w=400&h=300&fit=crop&auto=format',
            '丝瓜蛋汤': 'https://images.unsplash.com/photo-1547592166-23ac45744acd?w=400&h=300&fit=crop&auto=format',
            '拌木耳': 'https://images.unsplash.com/photo-1512621776951-a57141f2eefd?w=400&h=300&fit=crop&auto=format',
            '燕麦粥': 'https://images.unsplash.com/photo-1547592180-85f173990554?w=400&h=300&fit=crop&auto=format',
            '香菇蒸蛋': 'https://images.unsplash.com/photo-1506084868230-bb9d95c24759?w=400&h=300&fit=crop&auto=format',
            '冬瓜汤': 'https://images.unsplash.com/photo-1547592166-23ac45744acd?w=400&h=300&fit=crop&auto=format',
            '糙米饭': 'https://images.unsplash.com/photo-1586201375761-83865001e31c?w=400&h=300&fit=crop&auto=format',
            '番茄蛋汤': 'https://images.unsplash.com/photo-1547592166-23ac45744acd?w=400&h=300&fit=crop&auto=format',
            '拌萝卜丝': 'https://images.unsplash.com/photo-1512621776951-a57141f2eefd?w=400&h=300&fit=crop&auto=format',
            '黑芝麻糊': 'https://images.unsplash.com/photo-1544787219-7f47ccb76574?w=400&h=300&fit=crop&auto=format',
            '蒸南瓜': 'https://images.unsplash.com/photo-1570197788417-0e82375c9371?w=400&h=300&fit=crop&auto=format',
            '凉拌豆腐': 'https://images.unsplash.com/photo-1571167530149-c72f2b3d0fdc?w=400&h=300&fit=crop&auto=format',
            '黑米饭': 'https://images.unsplash.com/photo-1586201375761-83865001e31c?w=400&h=300&fit=crop&auto=format',
            '豆腐汤': 'https://images.unsplash.com/photo-1547592166-23ac45744acd?w=400&h=300&fit=crop&auto=format',
            '拌芹菜': 'https://images.unsplash.com/photo-1512621776951-a57141f2eefd?w=400&h=300&fit=crop&auto=format',
            '绿豆汤': 'https://images.unsplash.com/photo-1547592166-23ac45744acd?w=400&h=300&fit=crop&auto=format',
            '八宝粥': 'https://images.unsplash.com/photo-1547592180-85f173990554?w=400&h=300&fit=crop&auto=format',
            '鸡蛋挂面': 'https://images.unsplash.com/photo-1555126634-323283e090fa?w=400&h=300&fit=crop&auto=format',
            '银耳汤': 'https://images.unsplash.com/photo-1547592166-23ac45744acd?w=400&h=300&fit=crop&auto=format',
            '拌莴笋丝': 'https://images.unsplash.com/photo-1512621776951-a57141f2eefd?w=400&h=300&fit=crop&auto=format',
            '牛奶泡燕麦': 'https://images.unsplash.com/photo-1544787219-7f47ccb76574?w=400&h=300&fit=crop&auto=format',
            '腊肠焖饭': 'https://images.unsplash.com/photo-1586201375761-83865001e31c?w=400&h=300&fit=crop&auto=format',
            '拌海带丝': 'https://images.unsplash.com/photo-1512621776951-a57141f2eefd?w=400&h=300&fit=crop&auto=format',
            '豆浆冲蛋': 'https://images.unsplash.com/photo-1544787219-7f47ccb76574?w=400&h=300&fit=crop&auto=format',
            '全麦吐司': 'https://images.unsplash.com/photo-1509440159596-0249088772ff?w=400&h=300&fit=crop&auto=format',
            '虾仁蛋炒饭': 'https://images.unsplash.com/photo-1603133872878-684f208fb84b?w=400&h=300&fit=crop&auto=format',
            '拌黄瓜': 'https://images.unsplash.com/photo-1512621776951-a57141f2eefd?w=400&h=300&fit=crop&auto=format',
            '红枣豆浆': 'https://images.unsplash.com/photo-1544787219-7f47ccb76574?w=400&h=300&fit=crop&auto=format',
            '番茄鸡蛋盖饭': 'https://images.unsplash.com/photo-1603133872878-684f208fb84b?w=400&h=300&fit=crop&auto=format',
            '酸奶杯': 'https://images.unsplash.com/photo-1488477181946-6428a0291777?w=400&h=300&fit=crop&auto=format',
            '全麦面包': 'https://images.unsplash.com/photo-1509440159596-0249088772ff?w=400&h=300&fit=crop&auto=format',
            '青椒肉丝盖饭': 'https://images.unsplash.com/photo-1603133872878-684f208fb84b?w=400&h=300&fit=crop&auto=format',
            '花生酱吐司': 'https://images.unsplash.com/photo-1509440159596-0249088772ff?w=400&h=300&fit=crop&auto=format',
            '牛奶': 'https://images.unsplash.com/photo-1544787219-7f47ccb76574?w=400&h=300&fit=crop&auto=format',
            '冬瓜肉丸汤': 'https://images.unsplash.com/photo-1547592166-23ac45744acd?w=400&h=300&fit=crop&auto=format',
            '米饭': 'https://images.unsplash.com/photo-1586201375761-83865001e31c?w=400&h=300&fit=crop&auto=format'
        };

        // 获取菜品图片
        function getDishImage(dishName) {
            // 移除菜名中的修饰词，获取核心菜品名
            const cleanName = dishName.replace(/^(营养|美味|香嫩|清爽|爽口)/, '');

            // 返回预定义的图片，如果没有则使用默认图片
            return foodImages[cleanName] || foodImages[dishName] || 'https://images.unsplash.com/photo-1546069901-ba9599a7e63c?w=400&h=300&fit=crop&auto=format';
        }

        // 处理图片加载错误
        function handleImageError(img, mealType) {
            img.classList.add('error');
            img.style.backgroundImage = 'none';
            img.innerHTML = getEmojiForMeal(mealType);
            img.onerror = null; // 防止无限循环
        }

        // 根据餐次获取emoji
        function getEmojiForMeal(mealType) {
            const emojis = {
                '早餐': '🌅',
                '午餐': '🌞',
                '晚餐': '🌙'
            };
            return emojis[mealType] || '🍽️';
        }

        // 处理详情页图片加载错误
        function handleDishImageError(img, dishName) {
            img.style.background = 'linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%)';
            img.style.display = 'flex';
            img.style.alignItems = 'center';
            img.style.justifyContent = 'center';
            img.style.color = '#6c757d';
            img.style.fontSize = '2rem';
            img.innerHTML = getEmojiForDish(dishName);
            img.onerror = null;
        }

        // 为菜品获取合适的emoji
        function getEmojiForDish(dishName) {
            if (dishName.includes('蛋')) return '🥚';
            if (dishName.includes('粥')) return '🍲';
            if (dishName.includes('面')) return '🍜';
            if (dishName.includes('汤')) return '🍲';
            if (dishName.includes('菜')) return '🥬';
            if (dishName.includes('豆')) return '🫘';
            if (dishName.includes('肉')) return '🥩';
            if (dishName.includes('虾')) return '🦐';
            if (dishName.includes('饭')) return '🍚';
            if (dishName.includes('饼')) return '🥞';
            return '🍽️';
        }

        // 获取营养标签
        function getNutritionTags(dishName) {
            const tags = [];

            // 根据菜品名称判断营养特点
            if (dishName.includes('蛋') || dishName.includes('鸡蛋')) {
                tags.push('🥚 优质蛋白');
            }
            if (dishName.includes('菠菜') || dishName.includes('韭菜') || dishName.includes('青菜')) {
                tags.push('🥬 叶酸丰富');
            }
            if (dishName.includes('虾') || dishName.includes('肉')) {
                tags.push('🦐 高蛋白');
            }
            if (dishName.includes('豆腐') || dishName.includes('豆浆')) {
                tags.push('🦴 钙质补充');
            }
            if (dishName.includes('核桃') || dishName.includes('芝麻')) {
                tags.push('🌰 维生素E');
            }
            if (dishName.includes('红枣') || dishName.includes('红豆')) {
                tags.push('🩸 补血益气');
            }
            if (dishName.includes('小米') || dishName.includes('燕麦')) {
                tags.push('🌾 膳食纤维');
            }

            // 如果没有特定标签，添加通用标签
            if (tags.length === 0) {
                tags.push('🌟 营养均衡');
            }

            return tags;
        }

        // 估算烹饪时间
        function estimateCookingTime(steps) {
            let prepTime = 5; // 基础准备时间
            let cookTime = 10; // 基础烹饪时间

            // 根据步骤内容估算时间
            steps.forEach(step => {
                if (step.includes('浸泡') || step.includes('泡发')) {
                    prepTime += 30;
                }
                if (step.includes('腌制')) {
                    prepTime += 10;
                }
                if (step.includes('煮') && step.includes('分钟')) {
                    const match = step.match(/(\d+)分钟/);
                    if (match) {
                        cookTime += parseInt(match[1]);
                    }
                }
                if (step.includes('炒') || step.includes('煎')) {
                    cookTime += 5;
                }
                if (step.includes('蒸')) {
                    cookTime += 8;
                }
            });

            return {
                prep: `${prepTime}分钟`,
                cook: `${cookTime}分钟`,
                total: `${prepTime + cookTime}分钟`
            };
        }

        // 获取步骤时间提示
        function getStepTime(step) {
            const timeMatch = step.match(/(\d+)分钟/);
            if (timeMatch) {
                return timeMatch[1] + '分钟';
            }

            if (step.includes('大火')) return '大火';
            if (step.includes('小火')) return '小火';
            if (step.includes('中火')) return '中火';

            return null;
        }

        // 完整的30天菜谱数据
        const recipeData = {
            1: {
                title: "第1天",
                meals: {
                    breakfast: {
                        title: "营养蒸蛋 + 小米粥",
                        dishes: [
                            {
                                name: "营养蒸蛋",
                                ingredients: "鸡蛋1个、温水80ml、香油几滴、盐少许",
                                steps: [
                                    "将鸡蛋打散在碗中，加入少许盐搅拌均匀",
                                    "倒入温水（约40度），蛋液与水比例1:1.5",
                                    "用勺子撇去表面泡沫，保证蒸蛋光滑",
                                    "蒸锅水开后放入，中小火蒸6分钟",
                                    "出锅后滴几滴香油增香"
                                ],
                                tips: "温水调蛋口感更嫩，保鲜膜覆盖防水汽"
                            },
                            {
                                name: "小米粥",
                                ingredients: "小米50g、红枣3颗、清水500ml",
                                steps: [
                                    "小米用清水淘洗2-3遍至水清",
                                    "锅中加水烧开，倒入小米",
                                    "大火煮开后转小火，煮20分钟",
                                    "红枣去核切丁，最后5分钟加入",
                                    "煮至粥稠米烂即可"
                                ],
                                tips: "小火慢煮，偶尔搅拌防粘锅"
                            }
                        ]
                    },
                    lunch: {
                        title: "番茄鸡蛋面",
                        dishes: [
                            {
                                name: "番茄鸡蛋面",
                                ingredients: "挂面100g、番茄2个、鸡蛋1个、葱花适量、盐、生抽",
                                steps: [
                                    "番茄用开水烫30秒，去皮切块",
                                    "鸡蛋打散，热锅下油炒熟盛起",
                                    "锅中留底油，下番茄块炒出汁水，约3分钟",
                                    "加入500ml开水，大火煮开",
                                    "下入挂面，煮3分钟至软硬适中",
                                    "倒入炒蛋，加盐和生抽调味",
                                    "撒葱花即可出锅"
                                ],
                                tips: "番茄要炒出汁水，面条不要煮过软"
                            }
                        ]
                    },
                    dinner: {
                        title: "紫菜蛋花汤 + 凉拌黄瓜 + 小米粥",
                        dishes: [
                            {
                                name: "紫菜蛋花汤",
                                ingredients: "紫菜10g、鸡蛋半个、虾皮5g、香油、盐",
                                steps: [
                                    "紫菜用温水泡发2分钟，洗净备用",
                                    "鸡蛋打散，虾皮用温水泡发",
                                    "锅中加水500ml烧开",
                                    "下入紫菜和虾皮，煮2分钟",
                                    "将蛋液慢慢倒入，边倒边搅拌成蛋花",
                                    "加盐调味，滴香油即可"
                                ],
                                tips: "蛋液要慢慢倒入，形成漂亮蛋花"
                            },
                            {
                                name: "凉拌黄瓜",
                                ingredients: "黄瓜2根、蒜3瓣、香醋、香油、盐",
                                steps: [
                                    "黄瓜洗净，用刀拍碎后切段",
                                    "撒盐腌制10分钟，挤去多余水分",
                                    "蒜切蓉，加入香醋、香油调成汁",
                                    "将调料汁倒入黄瓜拌匀即可"
                                ],
                                tips: "拍碎的黄瓜更入味，腌制去除多余水分"
                            }
                        ]
                    }
                }
            },
            2: {
                title: "第2天",
                meals: {
                    breakfast: {
                        title: "豆浆 + 菠菜鸡蛋饼",
                        dishes: [
                            {
                                name: "豆浆",
                                ingredients: "黄豆50g、红枣3颗、清水800ml",
                                steps: [
                                    "黄豆提前一晚用清水浸泡8小时",
                                    "泡发的黄豆和红枣放入豆浆机",
                                    "加入清水至刻度线",
                                    "选择豆浆程序，约20分钟制作完成",
                                    "过滤后即可饮用"
                                ],
                                tips: "黄豆充分泡发，豆浆更香浓"
                            },
                            {
                                name: "菠菜鸡蛋饼",
                                ingredients: "菠菜100g、鸡蛋1个、面粉30g、盐、油",
                                steps: [
                                    "菠菜洗净，开水焯烫30秒后切碎",
                                    "鸡蛋打散，加入菠菜碎和面粉",
                                    "加少许盐调味，搅拌成无颗粒糊状",
                                    "平底锅刷少许油，小火加热",
                                    "倒入蛋糊摊成薄饼，煎2分钟",
                                    "翻面再煎2分钟至两面金黄"
                                ],
                                tips: "菠菜焯水去草酸，小火慢煎不易糊"
                            }
                        ]
                    },
                    lunch: {
                        title: "肉末蒸蛋羹 + 菠菜汤 + 米饭",
                        dishes: [
                            {
                                name: "肉末蒸蛋羹",
                                ingredients: "瘦肉末80g、鸡蛋1个、温水80ml、生抽、料酒、香油",
                                steps: [
                                    "肉末用料酒、生抽腌制5分钟",
                                    "热锅下油，炒肉末至变色，盛起晾凉",
                                    "鸡蛋打散，加温水搅匀",
                                    "将炒好的肉末倒入蛋液中",
                                    "蒸锅水开后放入，中火蒸8分钟",
                                    "出锅滴香油即可"
                                ],
                                tips: "肉末要先炒熟，蛋液过筛更嫩滑"
                            },
                            {
                                name: "菠菜汤",
                                ingredients: "菠菜200g、虾皮5g、盐、香油",
                                steps: [
                                    "菠菜洗净切段，虾皮温水泡发",
                                    "锅中加水400ml烧开",
                                    "下入菠菜煮2分钟至软身",
                                    "加入虾皮煮1分钟",
                                    "用盐调味，滴香油即可"
                                ],
                                tips: "菠菜不要煮太久，保持鲜绿色"
                            }
                        ]
                    },
                    dinner: {
                        title: "虾仁蒸蛋 + 凉拌豆腐丝 + 绿豆汤",
                        dishes: [
                            {
                                name: "虾仁蒸蛋",
                                ingredients: "鸡蛋1个、虾仁30g、温水80ml、盐、香油",
                                steps: [
                                    "虾仁去虾线，用盐抓洗后切碎",
                                    "鸡蛋打散，加温水和少许盐",
                                    "将虾仁碎倒入蛋液中拌匀",
                                    "蒸锅水开后放入，中火蒸6分钟",
                                    "出锅滴香油即可"
                                ],
                                tips: "虾仁要新鲜，切碎更易消化"
                            },
                            {
                                name: "凉拌豆腐丝",
                                ingredients: "豆腐丝200g、胡萝卜丝50g、香菜30g、生抽、香醋、香油",
                                steps: [
                                    "豆腐丝用开水烫1分钟，过凉水",
                                    "胡萝卜切丝，香菜切段",
                                    "将所有食材放入大碗",
                                    "加生抽、香醋、香油拌匀即可"
                                ],
                                tips: "豆腐丝烫水去豆腥味，过凉水保持爽脆"
                            }
                        ]
                    }
                }
            },
            3: {
                title: "第3天",
                meals: {
                    breakfast: { title: "核桃粥 + 水煮蛋", dishes: [{ name: "核桃粥", ingredients: "大米50g、核桃仁6个、清水600ml", steps: ["大米淘洗干净，核桃仁掰成小块", "锅中加水烧开，倒入大米", "大火煮开后转小火，煮25分钟", "加入核桃仁，继续煮5分钟", "煮至粥稠米烂即可"], tips: "核桃仁后加，避免煮太久变苦" }] },
                    lunch: { title: "虾仁蒸蛋 + 炒胡萝卜丝 + 小米饭", dishes: [{ name: "炒胡萝卜丝", ingredients: "胡萝卜2根、葱花、盐、油", steps: ["胡萝卜洗净去皮，擦成细丝", "热锅下油，爆香葱花", "下胡萝卜丝大火炒2分钟", "炒至软身变色，用盐调味即可"], tips: "大火快炒，保持脆嫩口感" }] },
                    dinner: { title: "冬瓜蛋汤 + 拌菠菜 + 小米粥", dishes: [{ name: "冬瓜蛋汤", ingredients: "冬瓜200g、鸡蛋1个、虾皮5g、香菜、盐、香油", steps: ["冬瓜去皮切薄片，鸡蛋打散", "锅中加水400ml烧开", "下入冬瓜片煮5分钟至透明", "加入虾皮煮1分钟", "倒入蛋液搅成蛋花", "用盐调味，撒香菜和香油即可"], tips: "冬瓜要煮透明，蛋液慢慢倒入" }] }
                }
            },
            4: {
                title: "第4天",
                meals: {
                    breakfast: { title: "红豆粥 + 韭菜炒鸡蛋", dishes: [{ name: "红豆粥", ingredients: "红豆50g、大米30g、清水700ml", steps: ["红豆提前一晚浸泡8小时", "锅中加水烧开，倒入红豆", "大火煮开后转小火，煮40分钟", "加入大米继续煮20分钟", "煮至红豆开花，粥稠即可"], tips: "红豆要充分泡发，先煮红豆再加大米" }] },
                    lunch: { title: "鸡蛋汤 + 炒青菜 + 红豆饭", dishes: [{ name: "炒青菜", ingredients: "小白菜300g、蒜3瓣、盐、油", steps: ["小白菜洗净切段，蒜切片", "热锅下油，爆香蒜片", "下青菜大火炒2分钟", "炒至软身，用盐调味即可"], tips: "大火快炒，保持青菜脆嫩" }] },
                    dinner: { title: "丝瓜蛋汤 + 拌木耳 + 燕麦粥", dishes: [{ name: "丝瓜蛋汤", ingredients: "丝瓜1根、鸡蛋半个、枸杞10粒、盐、香油", steps: ["丝瓜去皮切片，鸡蛋打散", "锅中加水400ml烧开", "下入丝瓜片煮3分钟至软", "倒入蛋液搅成蛋花", "加入枸杞煮1分钟", "用盐调味，滴香油即可"], tips: "丝瓜要煮软，枸杞最后加入" }] }
                }
            },
            5: {
                title: "第5天",
                meals: {
                    breakfast: { title: "燕麦粥 + 紫菜蛋花汤", dishes: [{ name: "燕麦粥", ingredients: "燕麦片40g、牛奶200ml、蜂蜜适量", steps: ["锅中加水200ml烧开", "倒入燕麦片煮5分钟", "加入牛奶煮2分钟", "关火后加蜂蜜调味即可"], tips: "牛奶不要煮太久，避免营养流失" }] },
                    lunch: { title: "香菇蒸蛋 + 冬瓜汤 + 糙米饭", dishes: [{ name: "香菇蒸蛋", ingredients: "鸡蛋1个、香菇3朵、胡萝卜丁30g、温水80ml、生抽、香油", steps: ["香菇用温水泡发30分钟，切丁", "胡萝卜洗净切小丁", "鸡蛋打散，加温水搅匀", "将香菇丁和胡萝卜丁倒入蛋液", "蒸锅水开后放入，中火蒸8分钟", "出锅后淋生抽和香油即可"], tips: "香菇要充分泡发，蔬菜丁要切小" }] },
                    dinner: { title: "番茄蛋汤 + 拌萝卜丝 + 小米粥", dishes: [{ name: "番茄蛋汤", ingredients: "番茄2个、鸡蛋半个、香葱、盐、香油", steps: ["番茄用开水烫去皮，切块", "鸡蛋打散，香葱切花", "热锅下油，炒番茄出汁3分钟", "加水400ml烧开", "倒入蛋液搅成蛋花", "用盐调味，撒葱花和香油即可"], tips: "番茄要炒出汁水，增加酸甜味" }] }
                }
            },
            6: {
                title: "第6天",
                meals: {
                    breakfast: { title: "黑芝麻糊 + 蒸南瓜", dishes: [{ name: "黑芝麻糊", ingredients: "黑芝麻粉30g、热水200ml、蜂蜜适量", steps: ["将黑芝麻粉倒入碗中", "慢慢加入热水，边加边搅拌", "搅拌至无颗粒状", "加蜂蜜调味即可"], tips: "要用热水冲调，搅拌充分避免结块" }] },
                    lunch: { title: "蛋花汤 + 凉拌豆腐 + 黑米饭", dishes: [{ name: "凉拌豆腐", ingredients: "嫩豆腐200g、生抽、香油、蒜蓉、香菜", steps: ["嫩豆腐切块，用开水烫1分钟", "捞出沥干水分，装盘", "蒜切蓉，香菜切段", "生抽、香油、蒜蓉调成汁", "淋在豆腐上，撒香菜即可"], tips: "豆腐要烫水去豆腥味，调料汁要拌匀" }] },
                    dinner: { title: "豆腐汤 + 拌芹菜 + 绿豆汤", dishes: [{ name: "拌芹菜", ingredients: "芹菜300g、花生米30g、盐、香油", steps: ["芹菜洗净切段，开水焯烫2分钟", "过凉水保持脆嫩，沥干水分", "花生米炸酥或用熟花生米", "芹菜加盐、香油拌匀", "撒上花生米即可"], tips: "芹菜焯水时间不要太长，保持脆嫩" }] }
                }
            },
            7: {
                title: "第7天",
                meals: {
                    breakfast: { title: "八宝粥 + 水煮蛋", dishes: [{ name: "八宝粥", ingredients: "八宝粥米50g、清水500ml", steps: ["八宝粥米提前浸泡2小时", "锅中加水烧开，倒入八宝粥米", "大火煮开后转小火，煮45分钟", "煮至粥稠米烂即可"], tips: "八宝粥米要充分泡发，小火慢煮" }] },
                    lunch: { title: "鸡蛋挂面", dishes: [{ name: "鸡蛋挂面", ingredients: "挂面100g、鸡蛋1个、青菜100g、紫菜5g、盐、香油", steps: ["锅中加水500ml烧开", "下入挂面煮2分钟", "打入鸡蛋，不要搅动，煮1分钟", "加入青菜和紫菜煮1分钟", "用盐调味，滴香油即可"], tips: "鸡蛋下锅后不要立即搅动，让蛋白凝固" }] },
                    dinner: { title: "银耳汤 + 拌莴笋丝 + 小米粥", dishes: [{ name: "银耳汤", ingredients: "银耳1朵、红枣5颗、桂圆8颗、冰糖适量", steps: ["银耳用温水泡发2小时，撕成小朵", "红枣去核，桂圆去壳", "锅中加水600ml，放入银耳", "大火煮开后转小火，煮30分钟", "加入红枣和桂圆煮15分钟", "最后加冰糖煮5分钟即可"], tips: "银耳要充分泡发，小火慢炖出胶质" }] }
                }
            },
            8: {
                title: "第8天",
                meals: {
                    breakfast: { title: "牛奶泡燕麦 + 水煮蛋", dishes: [{ name: "牛奶泡燕麦", ingredients: "燕麦片40g、热牛奶200ml、香蕉半根、核桃碎20g", steps: ["燕麦片放入碗中", "倒入热牛奶浸泡5分钟", "香蕉切片铺在上面", "撒上核桃碎即可"], tips: "牛奶要热，燕麦充分软化" }] },
                    lunch: { title: "腊肠焖饭", dishes: [{ name: "腊肠焖饭", ingredients: "大米100g、腊肠1根、胡萝卜丁50g、青豆30g、清水200ml", steps: ["大米淘洗干净，腊肠切片", "胡萝卜切小丁，青豆洗净", "电饭锅中放入大米和水", "铺上腊肠片、胡萝卜丁和青豆", "按煮饭键，约30分钟完成", "焖5分钟后拌匀即可"], tips: "腊肠要切薄片，蔬菜丁要切小" }] },
                    dinner: { title: "冬瓜汤 + 拌海带丝 + 小米粥", dishes: [{ name: "拌海带丝", ingredients: "海带丝200g、胡萝卜丝50g、蒜蓉、香醋、生抽、香油", steps: ["海带丝用温水泡发，洗净", "开水焯烫2分钟，过凉水", "胡萝卜切丝，开水焯烫1分钟", "所有食材放入碗中", "加调料拌匀即可"], tips: "海带丝要充分泡发，焯水去腥味" }] }
                }
            },
            9: {
                title: "第9天",
                meals: {
                    breakfast: { title: "豆浆冲蛋 + 全麦吐司", dishes: [{ name: "豆浆冲蛋", ingredients: "热豆浆200ml、生鸡蛋1个、蜂蜜适量", steps: ["鸡蛋打散在碗中", "慢慢倒入热豆浆，边倒边搅拌", "加蜂蜜调味即可"], tips: "豆浆要够热，能将蛋液冲熟" }] },
                    lunch: { title: "虾仁蛋炒饭", dishes: [{ name: "虾仁蛋炒饭", ingredients: "剩米饭200g、虾仁50g、鸡蛋1个、豌豆30g、胡萝卜丁30g、葱花、盐、生抽", steps: ["虾仁去虾线，用盐抓洗后切丁", "鸡蛋打散，热锅炒熟盛起", "锅中下油，炒虾仁至变色", "下米饭炒散，加豌豆和胡萝卜丁", "倒入炒蛋拌匀，用盐和生抽调味", "撒葱花即可出锅"], tips: "米饭要先炒散，避免结块" }] },
                    dinner: { title: "紫菜蛋花汤 + 拌黄瓜 + 燕麦粥", dishes: [{ name: "拌黄瓜", ingredients: "黄瓜2根、蒜3瓣、香醋、香油、盐", steps: ["黄瓜洗净，用刀拍碎后切段", "撒盐腌制10分钟，挤去多余水分", "蒜切蓉，加入香醋、香油调成汁", "将调料汁倒入黄瓜拌匀即可"], tips: "拍碎的黄瓜更入味，腌制去除多余水分" }] }
                }
            },
            10: {
                title: "第10天",
                meals: {
                    breakfast: { title: "红枣豆浆 + 菠菜鸡蛋饼", dishes: [{ name: "红枣豆浆", ingredients: "黄豆50g、红枣5颗、清水800ml", steps: ["黄豆提前浸泡8小时，红枣去核", "将黄豆和红枣放入豆浆机", "加水至刻度线，选择豆浆程序", "约20分钟制作完成，过滤即可"], tips: "红枣要去核，避免影响口感" }] },
                    lunch: { title: "番茄鸡蛋盖饭", dishes: [{ name: "番茄鸡蛋盖饭", ingredients: "米饭200g、番茄2个、鸡蛋1个、葱花、盐、糖、生抽", steps: ["番茄去皮切块，鸡蛋打散", "热锅下油，炒鸡蛋至半熟盛起", "锅中留油，炒番茄出汁", "加少许糖和盐调味", "倒入鸡蛋炒匀，淋生抽", "浇在米饭上，撒葱花即可"], tips: "鸡蛋要嫩滑，番茄要炒出汁" }] },
                    dinner: { title: "丝瓜蛋汤 + 凉拌豆腐丝 + 小米粥", dishes: [{ name: "凉拌豆腐丝", ingredients: "豆腐丝200g、胡萝卜丝50g、香菜30g、生抽、香醋、香油", steps: ["豆腐丝用开水烫1分钟，过凉水", "胡萝卜切丝，香菜切段", "将所有食材放入大碗", "加生抽、香醋、香油拌匀即可"], tips: "豆腐丝烫水去豆腥味，过凉水保持爽脆" }] }
                }
            }
        };

        // 为第11-30天生成简化的菜谱数据
        const simplifiedMeals = [
            { breakfast: "核桃豆浆 + 蒸蛋", lunch: "肉丝面", dinner: "冬瓜蛋汤 + 拌木耳 + 绿豆汤" },
            { breakfast: "小米南瓜粥 + 水煮蛋", lunch: "香菇鸡肉焖饭", dinner: "番茄蛋汤 + 拌萝卜丝 + 小米粥" },
            { breakfast: "酸奶杯 + 全麦面包", lunch: "青椒肉丝盖饭", dinner: "豆腐汤 + 拌芹菜 + 燕麦粥" },
            { breakfast: "花生酱吐司 + 牛奶", lunch: "冬瓜肉丸汤 + 米饭", dinner: "紫菜蛋花汤 + 凉拌黄瓜 + 小米粥" },
            { breakfast: "黑芝麻糊 + 水煮蛋", lunch: "白萝卜排骨汤 + 米饭", dinner: "丝瓜蛋汤 + 拌豆腐丝 + 绿豆汤" },
            { breakfast: "红枣枸杞茶 + 蒸蛋 + 馒头", lunch: "宫保鸡丁 + 米饭", dinner: "冬瓜汤 + 拌海带丝 + 小米粥" },
            { breakfast: "胡萝卜苹果汁 + 全麦吐司", lunch: "鱼香肉丝 + 米饭", dinner: "番茄蛋汤 + 拌莴笋丝 + 燕麦粥" },
            { breakfast: "豆浆 + 韭菜鸡蛋饼", lunch: "糖醋里脊 + 米饭", dinner: "豆腐汤 + 凉拌木耳 + 小米粥" },
            { breakfast: "紫薯牛奶 + 水煮蛋", lunch: "麻婆豆腐 + 米饭", dinner: "冬瓜蛋汤 + 拌萝卜丝 + 绿豆汤" },
            { breakfast: "蜂蜜柠檬水 + 蒸蛋 + 全麦面包", lunch: "土豆炖鸡块 + 米饭", dinner: "紫菜蛋花汤 + 拌芹菜 + 小米粥" },
            { breakfast: "八宝粥 + 水煮蛋", lunch: "萝卜炖牛肉 + 米饭", dinner: "丝瓜蛋汤 + 拌黄瓜 + 燕麦粥" },
            { breakfast: "红糖姜茶 + 蒸蛋 + 馒头", lunch: "豆腐炖鱼 + 米饭", dinner: "冬瓜汤 + 拌豆腐丝 + 小米粥" },
            { breakfast: "核桃粥 + 水煮蛋", lunch: "海带排骨汤 + 米饭", dinner: "番茄蛋汤 + 拌木耳 + 绿豆汤" },
            { breakfast: "豆浆 + 胡萝卜鸡蛋饼", lunch: "蒸蛋羹 + 炒青菜 + 红豆饭", dinner: "豆腐汤 + 拌海带丝 + 小米粥" },
            { breakfast: "黑芝麻糊 + 蒸南瓜", lunch: "虾仁面条", dinner: "紫菜蛋花汤 + 拌莴笋丝 + 燕麦粥" },
            { breakfast: "牛奶泡燕麦 + 水煮蛋", lunch: "番茄鸡蛋面", dinner: "冬瓜蛋汤 + 凉拌黄瓜 + 小米粥" },
            { breakfast: "红枣豆浆 + 菠菜鸡蛋饼", lunch: "肉末蒸蛋羹 + 菠菜汤 + 米饭", dinner: "丝瓜蛋汤 + 拌萝卜丝 + 绿豆汤" },
            { breakfast: "核桃豆浆 + 蒸蛋", lunch: "虾仁蛋炒饭", dinner: "番茄蛋汤 + 拌芹菜 + 小米粥" },
            { breakfast: "小米南瓜粥 + 水煮蛋", lunch: "腊肠焖饭", dinner: "豆腐汤 + 拌木耳 + 燕麦粥" },
            { breakfast: "八宝粥 + 水煮蛋", lunch: "鸡蛋挂面", dinner: "银耳汤 + 拌豆腐丝 + 小米粥" }
        ];

        // 第11-30天详细菜谱数据
        const detailedRecipes = {
            11: {
                title: "第11天",
                meals: {
                    breakfast: {
                        title: "核桃豆浆 + 蒸蛋",
                        dishes: [
                            { name: "核桃豆浆", ingredients: "黄豆50g、核桃仁6个、清水800ml", steps: ["黄豆提前浸泡8小时", "核桃仁掰成小块", "将黄豆和核桃放入豆浆机", "加水至刻度线，选择豆浆程序", "约20分钟制作完成，过滤即可"], tips: "核桃增加DHA，有助大脑发育" },
                            { name: "水蒸蛋", ingredients: "鸡蛋1个、温水80ml、盐少许、香油", steps: ["鸡蛋打散，加温水搅匀", "过筛去泡沫", "蒸锅水开后放入，中火蒸8分钟", "出锅后淋香油即可"], tips: "蛋液要过筛，口感更嫩滑" }
                        ]
                    },
                    lunch: {
                        title: "肉丝面",
                        dishes: [
                            { name: "肉丝面", ingredients: "挂面100g、猪肉丝80g、青菜100g、胡萝卜丝50g、葱花、生抽、盐、香油", steps: ["猪肉切丝，用生抽腌制10分钟", "锅中加水500ml烧开，下挂面煮2分钟", "另起锅炒肉丝至变色", "加入胡萝卜丝炒1分钟", "倒入面条和青菜煮1分钟", "用盐调味，撒葱花和香油即可"], tips: "肉丝要先腌制，面条不要煮太久" }
                        ]
                    },
                    dinner: {
                        title: "冬瓜蛋汤 + 拌木耳 + 绿豆汤",
                        dishes: [
                            { name: "冬瓜蛋汤", ingredients: "冬瓜200g、鸡蛋半个、虾皮5g、香菜、盐、香油", steps: ["冬瓜去皮切薄片，鸡蛋打散", "锅中加水400ml烧开", "下入冬瓜片煮5分钟至透明", "加入虾皮煮1分钟", "倒入蛋液搅成蛋花", "用盐调味，撒香菜和香油即可"], tips: "冬瓜要煮透明，蛋液慢慢倒入" },
                            { name: "拌木耳", ingredients: "黑木耳100g、胡萝卜丝30g、香菜20g、蒜蓉、香醋、生抽、香油", steps: ["黑木耳用温水泡发，洗净撕小朵", "开水焯烫2分钟，过凉水沥干", "胡萝卜切丝，香菜切段", "所有食材放入碗中", "加调料拌匀即可"], tips: "木耳要充分泡发，焯水去异味" }
                        ]
                    }
                }
            },
            12: {
                title: "第12天",
                meals: {
                    breakfast: {
                        title: "小米南瓜粥 + 水煮蛋",
                        dishes: [
                            { name: "小米南瓜粥", ingredients: "小米50g、南瓜100g、清水600ml", steps: ["小米淘洗干净，南瓜去皮切块", "锅中加水烧开，倒入小米", "大火煮开后转小火，煮20分钟", "加入南瓜块继续煮15分钟", "煮至粥稠南瓜软烂即可"], tips: "南瓜后加，避免煮太烂影响口感" },
                            { name: "水煮蛋", ingredients: "鸡蛋1个", steps: ["锅中加水烧开", "轻轻放入鸡蛋", "煮8分钟后捞出", "过凉水剥壳即可"], tips: "煮8分钟蛋黄刚好凝固，营养最佳" }
                        ]
                    },
                    lunch: {
                        title: "香菇鸡肉焖饭",
                        dishes: [
                            { name: "香菇鸡肉焖饭", ingredients: "大米100g、鸡胸肉80g、香菇5朵、胡萝卜丁50g、青豆30g、生抽、盐、香油", steps: ["大米淘洗干净，香菇泡发切丁", "鸡肉切丁，用生抽腌制", "电饭锅中放入大米和水200ml", "铺上鸡肉丁、香菇丁、胡萝卜丁和青豆", "按煮饭键，约30分钟完成", "焖5分钟后拌匀，淋香油即可"], tips: "鸡肉要腌制入味，蔬菜丁要切小" }
                        ]
                    },
                    dinner: {
                        title: "番茄蛋汤 + 拌萝卜丝 + 小米粥",
                        dishes: [
                            { name: "番茄蛋汤", ingredients: "番茄2个、鸡蛋半个、香葱、盐、香油", steps: ["番茄用开水烫去皮，切块", "鸡蛋打散，香葱切花", "热锅下油，炒番茄出汁3分钟", "加水400ml烧开", "倒入蛋液搅成蛋花", "用盐调味，撒葱花和香油即可"], tips: "番茄要炒出汁水，增加酸甜味" },
                            { name: "拌萝卜丝", ingredients: "白萝卜200g、胡萝卜50g、盐、香醋、香油", steps: ["白萝卜和胡萝卜洗净去皮，擦成丝", "撒盐腌制15分钟，挤去多余水分", "加香醋、香油拌匀即可"], tips: "腌制去除萝卜的辛辣味，口感更佳" }
                        ]
                    }
                }
            },
            13: {
                title: "第13天",
                meals: {
                    breakfast: {
                        title: "酸奶杯 + 全麦面包",
                        dishes: [
                            { name: "酸奶杯", ingredients: "原味酸奶200ml、蓝莓30g、燕麦片20g、蜂蜜适量", steps: ["酸奶倒入杯中", "铺上一层燕麦片", "放上蓝莓", "淋蜂蜜即可"], tips: "选择无糖酸奶，蓝莓富含花青素" },
                            { name: "全麦面包", ingredients: "全麦面包2片、牛油果半个、盐、黑胡椒", steps: ["牛油果去核压成泥", "加少许盐和黑胡椒调味", "涂抹在全麦面包上即可"], tips: "牛油果富含叶酸，是备孕佳品" }
                        ]
                    },
                    lunch: {
                        title: "青椒肉丝盖饭",
                        dishes: [
                            { name: "青椒肉丝盖饭", ingredients: "米饭200g、猪肉丝100g、青椒2个、胡萝卜丝50g、葱花、生抽、盐、淀粉", steps: ["猪肉切丝，用生抽和淀粉腌制", "青椒去籽切丝", "热锅下油，炒肉丝至变色盛起", "锅中留油，炒青椒丝2分钟", "倒入肉丝和胡萝卜丝炒匀", "用盐调味，浇在米饭上，撒葱花即可"], tips: "青椒不要炒太久，保持脆嫩" }
                        ]
                    },
                    dinner: {
                        title: "豆腐汤 + 拌芹菜 + 燕麦粥",
                        dishes: [
                            { name: "豆腐汤", ingredients: "嫩豆腐200g、紫菜5g、虾皮5g、香葱、盐、香油", steps: ["豆腐切块，紫菜撕小片", "锅中加水400ml烧开", "下入豆腐块煮3分钟", "加入紫菜和虾皮煮1分钟", "用盐调味，撒葱花和香油即可"], tips: "豆腐要轻轻煮，避免煮散" },
                            { name: "拌芹菜", ingredients: "芹菜300g、花生米30g、盐、香油", steps: ["芹菜洗净切段，开水焯烫2分钟", "过凉水保持脆嫩，沥干水分", "花生米炸酥或用熟花生米", "芹菜加盐、香油拌匀", "撒上花生米即可"], tips: "芹菜焯水时间不要太长，保持脆嫩" }
                        ]
                    }
                }
            },
            14: {
                title: "第14天",
                meals: {
                    breakfast: {
                        title: "花生酱吐司 + 牛奶",
                        dishes: [
                            { name: "花生酱吐司", ingredients: "全麦吐司2片、花生酱30g、香蕉半根", steps: ["吐司烤至微黄", "涂抹花生酱", "香蕉切片铺在上面即可"], tips: "花生酱富含蛋白质和维生素E" },
                            { name: "热牛奶", ingredients: "牛奶250ml", steps: ["牛奶倒入锅中", "小火加热至温热即可"], tips: "不要煮沸，避免营养流失" }
                        ]
                    },
                    lunch: {
                        title: "冬瓜肉丸汤 + 米饭",
                        dishes: [
                            { name: "冬瓜肉丸汤", ingredients: "冬瓜300g、猪肉馅100g、鸡蛋清半个、葱花、姜末、盐、淀粉", steps: ["冬瓜去皮切块", "肉馅加蛋清、葱花、姜末、盐、淀粉搅匀", "锅中加水500ml烧开", "用勺子挖肉馅做成丸子下锅", "煮5分钟后加入冬瓜块", "继续煮10分钟，用盐调味即可"], tips: "肉丸要现做现煮，口感更嫩" }
                        ]
                    },
                    dinner: {
                        title: "紫菜蛋花汤 + 凉拌黄瓜 + 小米粥",
                        dishes: [
                            { name: "紫菜蛋花汤", ingredients: "紫菜10g、鸡蛋半个、虾皮5g、香葱、盐、香油", steps: ["紫菜撕小片，鸡蛋打散", "锅中加水400ml烧开", "下入紫菜和虾皮煮2分钟", "倒入蛋液搅成蛋花", "用盐调味，撒葱花和香油即可"], tips: "紫菜富含碘，有助甲状腺健康" },
                            { name: "凉拌黄瓜", ingredients: "黄瓜2根、蒜3瓣、香醋、香油、盐", steps: ["黄瓜洗净，用刀拍碎后切段", "撒盐腌制10分钟，挤去多余水分", "蒜切蓉，加入香醋、香油调成汁", "将调料汁倒入黄瓜拌匀即可"], tips: "拍碎的黄瓜更入味，腌制去除多余水分" }
                        ]
                    }
                }
            },
            15: {
                title: "第15天",
                meals: {
                    breakfast: {
                        title: "黑芝麻糊 + 水煮蛋",
                        dishes: [
                            { name: "黑芝麻糊", ingredients: "黑芝麻粉30g、热水200ml、蜂蜜适量", steps: ["将黑芝麻粉倒入碗中", "慢慢加入热水，边加边搅拌", "搅拌至无颗粒状", "加蜂蜜调味即可"], tips: "要用热水冲调，搅拌充分避免结块" },
                            { name: "水煮蛋", ingredients: "鸡蛋1个", steps: ["锅中加水烧开", "轻轻放入鸡蛋", "煮8分钟后捞出", "过凉水剥壳即可"], tips: "煮8分钟蛋黄刚好凝固，营养最佳" }
                        ]
                    },
                    lunch: {
                        title: "白萝卜排骨汤 + 米饭",
                        dishes: [
                            { name: "白萝卜排骨汤", ingredients: "排骨300g、白萝卜200g、姜片、葱段、盐", steps: ["排骨洗净，开水焯烫去血沫", "白萝卜去皮切块", "锅中加水800ml，放入排骨和姜片", "大火煮开后转小火，炖40分钟", "加入萝卜块继续炖20分钟", "用盐调味，撒葱花即可"], tips: "排骨要先焯水，汤色更清澈" }
                        ]
                    },
                    dinner: {
                        title: "丝瓜蛋汤 + 拌豆腐丝 + 绿豆汤",
                        dishes: [
                            { name: "丝瓜蛋汤", ingredients: "丝瓜1根、鸡蛋半个、枸杞10粒、盐、香油", steps: ["丝瓜去皮切片，鸡蛋打散", "锅中加水400ml烧开", "下入丝瓜片煮3分钟至软", "倒入蛋液搅成蛋花", "加入枸杞煮1分钟", "用盐调味，滴香油即可"], tips: "丝瓜要煮软，枸杞最后加入" },
                            { name: "拌豆腐丝", ingredients: "豆腐丝200g、胡萝卜丝50g、香菜30g、生抽、香醋、香油", steps: ["豆腐丝用开水烫1分钟，过凉水", "胡萝卜切丝，香菜切段", "将所有食材放入大碗", "加生抽、香醋、香油拌匀即可"], tips: "豆腐丝烫水去豆腥味，过凉水保持爽脆" }
                        ]
                    }
                }
            }
        };

        // 继续添加第16-20天的详细菜谱
        const moreDetailedRecipes = {
            16: {
                title: "第16天",
                meals: {
                    breakfast: {
                        title: "红枣枸杞茶 + 蒸蛋 + 馒头",
                        dishes: [
                            { name: "红枣枸杞茶", ingredients: "红枣5颗、枸杞15粒、热水300ml", steps: ["红枣去核切片", "与枸杞一起放入杯中", "倒入热水浸泡10分钟即可"], tips: "红枣补血，枸杞明目养肝" },
                            { name: "蒸蛋", ingredients: "鸡蛋1个、温水80ml、盐少许、香油", steps: ["鸡蛋打散，加温水搅匀", "过筛去泡沫", "蒸锅水开后放入，中火蒸8分钟", "出锅后淋香油即可"], tips: "蛋液要过筛，口感更嫩滑" }
                        ]
                    },
                    lunch: {
                        title: "宫保鸡丁 + 米饭",
                        dishes: [
                            { name: "宫保鸡丁", ingredients: "鸡胸肉150g、花生米50g、青椒1个、胡萝卜50g、葱段、干辣椒、生抽、老抽、糖、醋、淀粉", steps: ["鸡肉切丁，用生抽和淀粉腌制", "青椒、胡萝卜切丁", "热锅下油，炸花生米至酥脆", "炒鸡丁至变色，加蔬菜丁炒2分钟", "调入调料炒匀，撒花生米即可"], tips: "鸡丁要腌制入味，火候要掌握好" }
                        ]
                    },
                    dinner: {
                        title: "冬瓜汤 + 拌海带丝 + 小米粥",
                        dishes: [
                            { name: "冬瓜汤", ingredients: "冬瓜200g、虾皮5g、香菜、盐、香油", steps: ["冬瓜去皮切片", "锅中加水400ml烧开", "下入冬瓜片煮5分钟", "加虾皮煮1分钟", "用盐调味，撒香菜和香油即可"], tips: "冬瓜清热利水，适合晚餐" },
                            { name: "拌海带丝", ingredients: "海带丝200g、胡萝卜丝50g、蒜蓉、香醋、生抽、香油", steps: ["海带丝用温水泡发，洗净", "开水焯烫2分钟，过凉水", "胡萝卜切丝，开水焯烫1分钟", "所有食材放入碗中", "加调料拌匀即可"], tips: "海带丝要充分泡发，焯水去腥味" }
                        ]
                    }
                }
            },
            17: {
                title: "第17天",
                meals: {
                    breakfast: {
                        title: "胡萝卜苹果汁 + 全麦吐司",
                        dishes: [
                            { name: "胡萝卜苹果汁", ingredients: "胡萝卜1根、苹果1个、柠檬汁几滴", steps: ["胡萝卜和苹果洗净去皮切块", "放入榨汁机榨汁", "加几滴柠檬汁防氧化即可"], tips: "胡萝卜富含胡萝卜素，苹果补充维生素" },
                            { name: "全麦吐司", ingredients: "全麦吐司2片、鸡蛋1个、盐、黑胡椒", steps: ["鸡蛋煎成荷包蛋", "夹在吐司中间", "撒少许盐和黑胡椒即可"], tips: "全麦面包富含膳食纤维" }
                        ]
                    },
                    lunch: {
                        title: "鱼香肉丝 + 米饭",
                        dishes: [
                            { name: "鱼香肉丝", ingredients: "猪肉丝150g、胡萝卜丝50g、青椒丝50g、木耳丝30g、葱花、蒜蓉、生抽、老抽、糖、醋、淀粉", steps: ["肉丝用生抽和淀粉腌制", "所有蔬菜切丝", "热锅下油，炒肉丝至变色", "加蔬菜丝炒2分钟", "调入鱼香汁炒匀即可"], tips: "鱼香汁要调好比例，酸甜适中" }
                        ]
                    },
                    dinner: {
                        title: "番茄蛋汤 + 拌莴笋丝 + 燕麦粥",
                        dishes: [
                            { name: "番茄蛋汤", ingredients: "番茄2个、鸡蛋半个、香葱、盐、香油", steps: ["番茄用开水烫去皮，切块", "鸡蛋打散，香葱切花", "热锅下油，炒番茄出汁3分钟", "加水400ml烧开", "倒入蛋液搅成蛋花", "用盐调味，撒葱花和香油即可"], tips: "番茄要炒出汁水，增加酸甜味" },
                            { name: "拌莴笋丝", ingredients: "莴笋300g、胡萝卜丝50g、盐、香醋、香油", steps: ["莴笋去皮切丝，撒盐腌制10分钟", "胡萝卜切丝", "挤去莴笋多余水分", "加胡萝卜丝、香醋、香油拌匀即可"], tips: "莴笋要腌制去涩味，口感更佳" }
                        ]
                    }
                }
            },
            18: {
                title: "第18天",
                meals: {
                    breakfast: {
                        title: "豆浆 + 韭菜鸡蛋饼",
                        dishes: [
                            { name: "豆浆", ingredients: "黄豆50g、清水800ml", steps: ["黄豆提前浸泡8小时", "放入豆浆机加水", "选择豆浆程序", "约20分钟制作完成，过滤即可"], tips: "黄豆要充分泡发，豆浆更香浓" },
                            { name: "韭菜鸡蛋饼", ingredients: "韭菜100g、鸡蛋2个、面粉50g、盐、油", steps: ["韭菜洗净切碎", "鸡蛋打散，加面粉调成糊", "加入韭菜和盐拌匀", "平底锅刷油，倒入蛋糊摊成饼", "两面煎至金黄即可"], tips: "韭菜富含叶酸，是备孕佳品" }
                        ]
                    },
                    lunch: {
                        title: "糖醋里脊 + 米饭",
                        dishes: [
                            { name: "糖醋里脊", ingredients: "猪里脊肉200g、青椒50g、胡萝卜50g、番茄酱、糖、醋、生抽、淀粉、蛋清", steps: ["里脊肉切条，用蛋清和淀粉腌制", "青椒、胡萝卜切块", "肉条过油炸至金黄", "锅中留少许油，炒蔬菜块", "调入糖醋汁，倒入里脊炒匀即可"], tips: "里脊要炸至外酥内嫩，糖醋汁要调好比例" }
                        ]
                    },
                    dinner: {
                        title: "豆腐汤 + 凉拌木耳 + 小米粥",
                        dishes: [
                            { name: "豆腐汤", ingredients: "嫩豆腐200g、紫菜5g、虾皮5g、香葱、盐、香油", steps: ["豆腐切块，紫菜撕小片", "锅中加水400ml烧开", "下入豆腐块煮3分钟", "加入紫菜和虾皮煮1分钟", "用盐调味，撒葱花和香油即可"], tips: "豆腐要轻轻煮，避免煮散" },
                            { name: "凉拌木耳", ingredients: "黑木耳100g、胡萝卜丝30g、香菜20g、蒜蓉、香醋、生抽、香油", steps: ["黑木耳用温水泡发，洗净撕小朵", "开水焯烫2分钟，过凉水沥干", "胡萝卜切丝，香菜切段", "所有食材放入碗中", "加调料拌匀即可"], tips: "木耳要充分泡发，焯水去异味" }
                        ]
                    }
                }
            },
            19: {
                title: "第19天",
                meals: {
                    breakfast: {
                        title: "紫薯牛奶 + 水煮蛋",
                        dishes: [
                            { name: "紫薯牛奶", ingredients: "紫薯100g、牛奶200ml、蜂蜜适量", steps: ["紫薯蒸熟去皮", "与牛奶一起放入料理机打成糊", "加蜂蜜调味即可"], tips: "紫薯富含花青素，抗氧化效果好" },
                            { name: "水煮蛋", ingredients: "鸡蛋1个", steps: ["锅中加水烧开", "轻轻放入鸡蛋", "煮8分钟后捞出", "过凉水剥壳即可"], tips: "煮8分钟蛋黄刚好凝固，营养最佳" }
                        ]
                    },
                    lunch: {
                        title: "麻婆豆腐 + 米饭",
                        dishes: [
                            { name: "麻婆豆腐", ingredients: "嫩豆腐300g、猪肉末50g、豆瓣酱、葱花、蒜蓉、生抽、淀粉水", steps: ["豆腐切块，开水焯烫去豆腥", "热锅下油，炒肉末至变色", "加豆瓣酱炒出红油", "倒入豆腐块轻轻炒匀", "用淀粉水勾芡，撒葱花即可"], tips: "豆腐要先焯水，炒制时动作要轻" }
                        ]
                    },
                    dinner: {
                        title: "冬瓜蛋汤 + 拌萝卜丝 + 绿豆汤",
                        dishes: [
                            { name: "冬瓜蛋汤", ingredients: "冬瓜200g、鸡蛋半个、虾皮5g、香菜、盐、香油", steps: ["冬瓜去皮切薄片，鸡蛋打散", "锅中加水400ml烧开", "下入冬瓜片煮5分钟至透明", "加入虾皮煮1分钟", "倒入蛋液搅成蛋花", "用盐调味，撒香菜和香油即可"], tips: "冬瓜要煮透明，蛋液慢慢倒入" },
                            { name: "拌萝卜丝", ingredients: "白萝卜200g、胡萝卜50g、盐、香醋、香油", steps: ["白萝卜和胡萝卜洗净去皮，擦成丝", "撒盐腌制15分钟，挤去多余水分", "加香醋、香油拌匀即可"], tips: "腌制去除萝卜的辛辣味，口感更佳" }
                        ]
                    }
                }
            },
            20: {
                title: "第20天",
                meals: {
                    breakfast: {
                        title: "蜂蜜柠檬水 + 蒸蛋 + 全麦面包",
                        dishes: [
                            { name: "蜂蜜柠檬水", ingredients: "柠檬半个、蜂蜜20g、温水300ml", steps: ["柠檬洗净切片", "放入杯中加蜂蜜", "倒入温水搅拌均匀即可"], tips: "柠檬富含维生素C，增强免疫力" },
                            { name: "蒸蛋", ingredients: "鸡蛋1个、温水80ml、盐少许、香油", steps: ["鸡蛋打散，加温水搅匀", "过筛去泡沫", "蒸锅水开后放入，中火蒸8分钟", "出锅后淋香油即可"], tips: "蛋液要过筛，口感更嫩滑" }
                        ]
                    },
                    lunch: {
                        title: "土豆炖鸡块 + 米饭",
                        dishes: [
                            { name: "土豆炖鸡块", ingredients: "鸡腿肉300g、土豆200g、胡萝卜100g、葱段、姜片、生抽、老抽、盐、糖", steps: ["鸡肉切块，开水焯烫去血沫", "土豆、胡萝卜切块", "热锅下油，炒鸡块至微黄", "加葱姜炒香，倒入生抽老抽上色", "加水没过鸡块，大火煮开转小火炖20分钟", "加入土豆胡萝卜继续炖15分钟", "用盐糖调味即可"], tips: "鸡肉要先焯水，土豆后加避免炖烂" }
                        ]
                    },
                    dinner: {
                        title: "紫菜蛋花汤 + 拌芹菜 + 小米粥",
                        dishes: [
                            { name: "紫菜蛋花汤", ingredients: "紫菜10g、鸡蛋半个、虾皮5g、香葱、盐、香油", steps: ["紫菜撕小片，鸡蛋打散", "锅中加水400ml烧开", "下入紫菜和虾皮煮2分钟", "倒入蛋液搅成蛋花", "用盐调味，撒葱花和香油即可"], tips: "紫菜富含碘，有助甲状腺健康" },
                            { name: "拌芹菜", ingredients: "芹菜300g、花生米30g、盐、香油", steps: ["芹菜洗净切段，开水焯烫2分钟", "过凉水保持脆嫩，沥干水分", "花生米炸酥或用熟花生米", "芹菜加盐、香油拌匀", "撒上花生米即可"], tips: "芹菜焯水时间不要太长，保持脆嫩" }
                        ]
                    }
                }
            }
        };

        // 继续添加第21-25天的详细菜谱
        const finalDetailedRecipes1 = {
            21: {
                title: "第21天",
                meals: {
                    breakfast: {
                        title: "八宝粥 + 水煮蛋",
                        dishes: [
                            { name: "八宝粥", ingredients: "八宝粥米50g、清水500ml", steps: ["八宝粥米提前浸泡2小时", "锅中加水烧开，倒入八宝粥米", "大火煮开后转小火，煮45分钟", "煮至粥稠米烂即可"], tips: "八宝粥米要充分泡发，小火慢煮" },
                            { name: "水煮蛋", ingredients: "鸡蛋1个", steps: ["锅中加水烧开", "轻轻放入鸡蛋", "煮8分钟后捞出", "过凉水剥壳即可"], tips: "煮8分钟蛋黄刚好凝固，营养最佳" }
                        ]
                    },
                    lunch: {
                        title: "萝卜炖牛肉 + 米饭",
                        dishes: [
                            { name: "萝卜炖牛肉", ingredients: "牛肉300g、白萝卜200g、胡萝卜100g、葱段、姜片、八角、生抽、老抽、盐", steps: ["牛肉切块，开水焯烫去血沫", "萝卜去皮切块", "热锅下油，炒牛肉块至微黄", "加葱姜八角炒香", "倒入生抽老抽上色", "加水没过牛肉，大火煮开转小火炖1小时", "加入萝卜块继续炖30分钟", "用盐调味即可"], tips: "牛肉要炖至软烂，萝卜后加避免炖烂" }
                        ]
                    },
                    dinner: {
                        title: "丝瓜蛋汤 + 拌黄瓜 + 燕麦粥",
                        dishes: [
                            { name: "丝瓜蛋汤", ingredients: "丝瓜1根、鸡蛋半个、枸杞10粒、盐、香油", steps: ["丝瓜去皮切片，鸡蛋打散", "锅中加水400ml烧开", "下入丝瓜片煮3分钟至软", "倒入蛋液搅成蛋花", "加入枸杞煮1分钟", "用盐调味，滴香油即可"], tips: "丝瓜要煮软，枸杞最后加入" },
                            { name: "拌黄瓜", ingredients: "黄瓜2根、蒜3瓣、香醋、香油、盐", steps: ["黄瓜洗净，用刀拍碎后切段", "撒盐腌制10分钟，挤去多余水分", "蒜切蓉，加入香醋、香油调成汁", "将调料汁倒入黄瓜拌匀即可"], tips: "拍碎的黄瓜更入味，腌制去除多余水分" }
                        ]
                    }
                }
            },
            22: {
                title: "第22天",
                meals: {
                    breakfast: {
                        title: "红糖姜茶 + 蒸蛋 + 馒头",
                        dishes: [
                            { name: "红糖姜茶", ingredients: "生姜3片、红糖20g、热水300ml", steps: ["生姜洗净切片", "放入杯中加红糖", "倒入热水浸泡10分钟即可"], tips: "生姜温胃，红糖补血，适合女性" },
                            { name: "蒸蛋", ingredients: "鸡蛋1个、温水80ml、盐少许、香油", steps: ["鸡蛋打散，加温水搅匀", "过筛去泡沫", "蒸锅水开后放入，中火蒸8分钟", "出锅后淋香油即可"], tips: "蛋液要过筛，口感更嫩滑" }
                        ]
                    },
                    lunch: {
                        title: "豆腐炖鱼 + 米饭",
                        dishes: [
                            { name: "豆腐炖鱼", ingredients: "鲫鱼1条、豆腐200g、葱段、姜片、料酒、盐、胡椒粉", steps: ["鲫鱼处理干净，豆腐切块", "热锅下油，煎鱼至两面金黄", "加葱姜爆香，倒入料酒", "加水没过鱼身，大火煮开", "转中火炖15分钟至汤色发白", "加入豆腐块继续炖10分钟", "用盐和胡椒粉调味即可"], tips: "鱼要先煎定型，大火炖出奶白汤色" }
                        ]
                    },
                    dinner: {
                        title: "冬瓜汤 + 拌豆腐丝 + 小米粥",
                        dishes: [
                            { name: "冬瓜汤", ingredients: "冬瓜200g、虾皮5g、香菜、盐、香油", steps: ["冬瓜去皮切片", "锅中加水400ml烧开", "下入冬瓜片煮5分钟", "加虾皮煮1分钟", "用盐调味，撒香菜和香油即可"], tips: "冬瓜清热利水，适合晚餐" },
                            { name: "拌豆腐丝", ingredients: "豆腐丝200g、胡萝卜丝50g、香菜30g、生抽、香醋、香油", steps: ["豆腐丝用开水烫1分钟，过凉水", "胡萝卜切丝，香菜切段", "将所有食材放入大碗", "加生抽、香醋、香油拌匀即可"], tips: "豆腐丝烫水去豆腥味，过凉水保持爽脆" }
                        ]
                    }
                }
            },
            23: {
                title: "第23天",
                meals: {
                    breakfast: {
                        title: "核桃粥 + 水煮蛋",
                        dishes: [
                            { name: "核桃粥", ingredients: "大米50g、核桃仁6个、清水600ml", steps: ["大米淘洗干净，核桃仁掰成小块", "锅中加水烧开，倒入大米", "大火煮开后转小火，煮25分钟", "加入核桃仁，继续煮5分钟", "煮至粥稠米烂即可"], tips: "核桃仁后加，避免煮太久变苦" },
                            { name: "水煮蛋", ingredients: "鸡蛋1个", steps: ["锅中加水烧开", "轻轻放入鸡蛋", "煮8分钟后捞出", "过凉水剥壳即可"], tips: "煮8分钟蛋黄刚好凝固，营养最佳" }
                        ]
                    },
                    lunch: {
                        title: "海带排骨汤 + 米饭",
                        dishes: [
                            { name: "海带排骨汤", ingredients: "排骨300g、海带100g、冬瓜100g、葱段、姜片、盐", steps: ["排骨洗净，开水焯烫去血沫", "海带用温水泡发，洗净切段", "冬瓜去皮切块", "锅中加水800ml，放入排骨和姜片", "大火煮开后转小火，炖40分钟", "加入海带继续炖15分钟", "最后加冬瓜炖5分钟，用盐调味即可"], tips: "海带要充分泡发，冬瓜最后加入" }
                        ]
                    },
                    dinner: {
                        title: "番茄蛋汤 + 拌木耳 + 绿豆汤",
                        dishes: [
                            { name: "番茄蛋汤", ingredients: "番茄2个、鸡蛋半个、香葱、盐、香油", steps: ["番茄用开水烫去皮，切块", "鸡蛋打散，香葱切花", "热锅下油，炒番茄出汁3分钟", "加水400ml烧开", "倒入蛋液搅成蛋花", "用盐调味，撒葱花和香油即可"], tips: "番茄要炒出汁水，增加酸甜味" },
                            { name: "拌木耳", ingredients: "黑木耳100g、胡萝卜丝30g、香菜20g、蒜蓉、香醋、生抽、香油", steps: ["黑木耳用温水泡发，洗净撕小朵", "开水焯烫2分钟，过凉水沥干", "胡萝卜切丝，香菜切段", "所有食材放入碗中", "加调料拌匀即可"], tips: "木耳要充分泡发，焯水去异味" }
                        ]
                    }
                }
            },
            24: {
                title: "第24天",
                meals: {
                    breakfast: {
                        title: "豆浆 + 胡萝卜鸡蛋饼",
                        dishes: [
                            { name: "豆浆", ingredients: "黄豆50g、清水800ml", steps: ["黄豆提前浸泡8小时", "放入豆浆机加水", "选择豆浆程序", "约20分钟制作完成，过滤即可"], tips: "黄豆要充分泡发，豆浆更香浓" },
                            { name: "胡萝卜鸡蛋饼", ingredients: "胡萝卜100g、鸡蛋2个、面粉50g、盐、油", steps: ["胡萝卜洗净擦丝", "鸡蛋打散，加面粉调成糊", "加入胡萝卜丝和盐拌匀", "平底锅刷油，倒入蛋糊摊成饼", "两面煎至金黄即可"], tips: "胡萝卜富含胡萝卜素，有益视力" }
                        ]
                    },
                    lunch: {
                        title: "蒸蛋羹 + 炒青菜 + 红豆饭",
                        dishes: [
                            { name: "蒸蛋羹", ingredients: "鸡蛋2个、温水160ml、虾仁50g、香菇2朵、盐、香油", steps: ["鸡蛋打散，加温水搅匀", "虾仁去虾线，香菇切丁", "将虾仁和香菇丁加入蛋液", "过筛去泡沫", "蒸锅水开后放入，中火蒸12分钟", "出锅后淋香油即可"], tips: "蛋液要过筛，蒸制时间要掌握好" },
                            { name: "炒青菜", ingredients: "小白菜300g、蒜3瓣、盐、油", steps: ["小白菜洗净切段，蒜切片", "热锅下油，爆香蒜片", "下青菜大火炒2分钟", "炒至软身，用盐调味即可"], tips: "大火快炒，保持青菜脆嫩" }
                        ]
                    },
                    dinner: {
                        title: "豆腐汤 + 拌海带丝 + 小米粥",
                        dishes: [
                            { name: "豆腐汤", ingredients: "嫩豆腐200g、紫菜5g、虾皮5g、香葱、盐、香油", steps: ["豆腐切块，紫菜撕小片", "锅中加水400ml烧开", "下入豆腐块煮3分钟", "加入紫菜和虾皮煮1分钟", "用盐调味，撒葱花和香油即可"], tips: "豆腐要轻轻煮，避免煮散" },
                            { name: "拌海带丝", ingredients: "海带丝200g、胡萝卜丝50g、蒜蓉、香醋、生抽、香油", steps: ["海带丝用温水泡发，洗净", "开水焯烫2分钟，过凉水", "胡萝卜切丝，开水焯烫1分钟", "所有食材放入碗中", "加调料拌匀即可"], tips: "海带丝要充分泡发，焯水去腥味" }
                        ]
                    }
                }
            },
            25: {
                title: "第25天",
                meals: {
                    breakfast: {
                        title: "黑芝麻糊 + 蒸南瓜",
                        dishes: [
                            { name: "黑芝麻糊", ingredients: "黑芝麻粉30g、热水200ml、蜂蜜适量", steps: ["将黑芝麻粉倒入碗中", "慢慢加入热水，边加边搅拌", "搅拌至无颗粒状", "加蜂蜜调味即可"], tips: "要用热水冲调，搅拌充分避免结块" },
                            { name: "蒸南瓜", ingredients: "南瓜200g", steps: ["南瓜去皮切块", "蒸锅水开后放入", "蒸15分钟至软烂即可"], tips: "南瓜富含胡萝卜素和膳食纤维" }
                        ]
                    },
                    lunch: {
                        title: "虾仁面条",
                        dishes: [
                            { name: "虾仁面条", ingredients: "挂面100g、虾仁80g、青菜100g、鸡蛋半个、葱花、盐、香油", steps: ["虾仁去虾线，用盐抓洗", "锅中加水500ml烧开，下挂面煮2分钟", "加入虾仁煮1分钟", "打入鸡蛋，不要搅动", "加入青菜煮1分钟", "用盐调味，撒葱花和香油即可"], tips: "虾仁要新鲜，鸡蛋下锅后不要立即搅动" }
                        ]
                    },
                    dinner: {
                        title: "紫菜蛋花汤 + 拌莴笋丝 + 燕麦粥",
                        dishes: [
                            { name: "紫菜蛋花汤", ingredients: "紫菜10g、鸡蛋半个、虾皮5g、香葱、盐、香油", steps: ["紫菜撕小片，鸡蛋打散", "锅中加水400ml烧开", "下入紫菜和虾皮煮2分钟", "倒入蛋液搅成蛋花", "用盐调味，撒葱花和香油即可"], tips: "紫菜富含碘，有助甲状腺健康" },
                            { name: "拌莴笋丝", ingredients: "莴笋300g、胡萝卜丝50g、盐、香醋、香油", steps: ["莴笋去皮切丝，撒盐腌制10分钟", "胡萝卜切丝", "挤去莴笋多余水分", "加胡萝卜丝、香醋、香油拌匀即可"], tips: "莴笋要腌制去涩味，口感更佳" }
                        ]
                    }
                }
            }
        };

        // 最后添加第26-30天的详细菜谱
        const finalDetailedRecipes2 = {
            26: {
                title: "第26天",
                meals: {
                    breakfast: {
                        title: "牛奶泡燕麦 + 水煮蛋",
                        dishes: [
                            { name: "牛奶泡燕麦", ingredients: "燕麦片40g、热牛奶200ml、香蕉半根、核桃碎20g", steps: ["燕麦片放入碗中", "倒入热牛奶浸泡5分钟", "香蕉切片铺在上面", "撒上核桃碎即可"], tips: "牛奶要热，燕麦充分软化" },
                            { name: "水煮蛋", ingredients: "鸡蛋1个", steps: ["锅中加水烧开", "轻轻放入鸡蛋", "煮8分钟后捞出", "过凉水剥壳即可"], tips: "煮8分钟蛋黄刚好凝固，营养最佳" }
                        ]
                    },
                    lunch: {
                        title: "番茄鸡蛋面",
                        dishes: [
                            { name: "番茄鸡蛋面", ingredients: "挂面100g、番茄2个、鸡蛋1个、葱花、盐、糖、生抽", steps: ["番茄去皮切块，鸡蛋打散", "热锅下油，炒鸡蛋至半熟盛起", "锅中留油，炒番茄出汁", "加少许糖和盐调味", "加水400ml烧开，下挂面煮2分钟", "倒入鸡蛋炒匀，淋生抽", "撒葱花即可"], tips: "鸡蛋要嫩滑，番茄要炒出汁" }
                        ]
                    },
                    dinner: {
                        title: "冬瓜蛋汤 + 凉拌黄瓜 + 小米粥",
                        dishes: [
                            { name: "冬瓜蛋汤", ingredients: "冬瓜200g、鸡蛋半个、虾皮5g、香菜、盐、香油", steps: ["冬瓜去皮切薄片，鸡蛋打散", "锅中加水400ml烧开", "下入冬瓜片煮5分钟至透明", "加入虾皮煮1分钟", "倒入蛋液搅成蛋花", "用盐调味，撒香菜和香油即可"], tips: "冬瓜要煮透明，蛋液慢慢倒入" },
                            { name: "凉拌黄瓜", ingredients: "黄瓜2根、蒜3瓣、香醋、香油、盐", steps: ["黄瓜洗净，用刀拍碎后切段", "撒盐腌制10分钟，挤去多余水分", "蒜切蓉，加入香醋、香油调成汁", "将调料汁倒入黄瓜拌匀即可"], tips: "拍碎的黄瓜更入味，腌制去除多余水分" }
                        ]
                    }
                }
            },
            27: {
                title: "第27天",
                meals: {
                    breakfast: {
                        title: "红枣豆浆 + 菠菜鸡蛋饼",
                        dishes: [
                            { name: "红枣豆浆", ingredients: "黄豆50g、红枣5颗、清水800ml", steps: ["黄豆提前浸泡8小时，红枣去核", "将黄豆和红枣放入豆浆机", "加水至刻度线，选择豆浆程序", "约20分钟制作完成，过滤即可"], tips: "红枣要去核，避免影响口感" },
                            { name: "菠菜鸡蛋饼", ingredients: "菠菜100g、鸡蛋2个、面粉50g、盐、油", steps: ["菠菜洗净焯水切碎", "鸡蛋打散，加面粉调成糊", "加入菠菜碎和盐拌匀", "平底锅刷油，倒入蛋糊摊成饼", "两面煎至金黄即可"], tips: "菠菜富含叶酸，是备孕必备" }
                        ]
                    },
                    lunch: {
                        title: "肉末蒸蛋羹 + 菠菜汤 + 米饭",
                        dishes: [
                            { name: "肉末蒸蛋羹", ingredients: "鸡蛋2个、猪肉末50g、温水160ml、生抽、盐、香油", steps: ["猪肉末用生抽腌制10分钟", "鸡蛋打散，加温水搅匀", "将肉末加入蛋液中", "过筛去泡沫", "蒸锅水开后放入，中火蒸12分钟", "出锅后淋香油即可"], tips: "肉末要先腌制，蛋液要过筛" },
                            { name: "菠菜汤", ingredients: "菠菜200g、虾皮5g、盐、香油", steps: ["菠菜洗净切段", "锅中加水400ml烧开", "下入菠菜煮2分钟", "加虾皮煮1分钟", "用盐调味，滴香油即可"], tips: "菠菜不要煮太久，保持营养" }
                        ]
                    },
                    dinner: {
                        title: "丝瓜蛋汤 + 拌萝卜丝 + 绿豆汤",
                        dishes: [
                            { name: "丝瓜蛋汤", ingredients: "丝瓜1根、鸡蛋半个、枸杞10粒、盐、香油", steps: ["丝瓜去皮切片，鸡蛋打散", "锅中加水400ml烧开", "下入丝瓜片煮3分钟至软", "倒入蛋液搅成蛋花", "加入枸杞煮1分钟", "用盐调味，滴香油即可"], tips: "丝瓜要煮软，枸杞最后加入" },
                            { name: "拌萝卜丝", ingredients: "白萝卜200g、胡萝卜50g、盐、香醋、香油", steps: ["白萝卜和胡萝卜洗净去皮，擦成丝", "撒盐腌制15分钟，挤去多余水分", "加香醋、香油拌匀即可"], tips: "腌制去除萝卜的辛辣味，口感更佳" }
                        ]
                    }
                }
            },
            28: {
                title: "第28天",
                meals: {
                    breakfast: {
                        title: "核桃豆浆 + 蒸蛋",
                        dishes: [
                            { name: "核桃豆浆", ingredients: "黄豆50g、核桃仁6个、清水800ml", steps: ["黄豆提前浸泡8小时", "核桃仁掰成小块", "将黄豆和核桃放入豆浆机", "加水至刻度线，选择豆浆程序", "约20分钟制作完成，过滤即可"], tips: "核桃增加DHA，有助大脑发育" },
                            { name: "蒸蛋", ingredients: "鸡蛋1个、温水80ml、盐少许、香油", steps: ["鸡蛋打散，加温水搅匀", "过筛去泡沫", "蒸锅水开后放入，中火蒸8分钟", "出锅后淋香油即可"], tips: "蛋液要过筛，口感更嫩滑" }
                        ]
                    },
                    lunch: {
                        title: "虾仁蛋炒饭",
                        dishes: [
                            { name: "虾仁蛋炒饭", ingredients: "剩米饭200g、虾仁50g、鸡蛋1个、豌豆30g、胡萝卜丁30g、葱花、盐、生抽", steps: ["虾仁去虾线，用盐抓洗后切丁", "鸡蛋打散，热锅炒熟盛起", "锅中下油，炒虾仁至变色", "下米饭炒散，加豌豆和胡萝卜丁", "倒入炒蛋拌匀，用盐和生抽调味", "撒葱花即可出锅"], tips: "米饭要先炒散，避免结块" }
                        ]
                    },
                    dinner: {
                        title: "番茄蛋汤 + 拌芹菜 + 小米粥",
                        dishes: [
                            { name: "番茄蛋汤", ingredients: "番茄2个、鸡蛋半个、香葱、盐、香油", steps: ["番茄用开水烫去皮，切块", "鸡蛋打散，香葱切花", "热锅下油，炒番茄出汁3分钟", "加水400ml烧开", "倒入蛋液搅成蛋花", "用盐调味，撒葱花和香油即可"], tips: "番茄要炒出汁水，增加酸甜味" },
                            { name: "拌芹菜", ingredients: "芹菜300g、花生米30g、盐、香油", steps: ["芹菜洗净切段，开水焯烫2分钟", "过凉水保持脆嫩，沥干水分", "花生米炸酥或用熟花生米", "芹菜加盐、香油拌匀", "撒上花生米即可"], tips: "芹菜焯水时间不要太长，保持脆嫩" }
                        ]
                    }
                }
            },
            29: {
                title: "第29天",
                meals: {
                    breakfast: {
                        title: "小米南瓜粥 + 水煮蛋",
                        dishes: [
                            { name: "小米南瓜粥", ingredients: "小米50g、南瓜100g、清水600ml", steps: ["小米淘洗干净，南瓜去皮切块", "锅中加水烧开，倒入小米", "大火煮开后转小火，煮20分钟", "加入南瓜块继续煮15分钟", "煮至粥稠南瓜软烂即可"], tips: "南瓜后加，避免煮太烂影响口感" },
                            { name: "水煮蛋", ingredients: "鸡蛋1个", steps: ["锅中加水烧开", "轻轻放入鸡蛋", "煮8分钟后捞出", "过凉水剥壳即可"], tips: "煮8分钟蛋黄刚好凝固，营养最佳" }
                        ]
                    },
                    lunch: {
                        title: "腊肠焖饭",
                        dishes: [
                            { name: "腊肠焖饭", ingredients: "大米100g、腊肠1根、胡萝卜丁50g、青豆30g、清水200ml", steps: ["大米淘洗干净，腊肠切片", "胡萝卜切小丁，青豆洗净", "电饭锅中放入大米和水", "铺上腊肠片、胡萝卜丁和青豆", "按煮饭键，约30分钟完成", "焖5分钟后拌匀即可"], tips: "腊肠要切薄片，蔬菜丁要切小" }
                        ]
                    },
                    dinner: {
                        title: "豆腐汤 + 拌木耳 + 燕麦粥",
                        dishes: [
                            { name: "豆腐汤", ingredients: "嫩豆腐200g、紫菜5g、虾皮5g、香葱、盐、香油", steps: ["豆腐切块，紫菜撕小片", "锅中加水400ml烧开", "下入豆腐块煮3分钟", "加入紫菜和虾皮煮1分钟", "用盐调味，撒葱花和香油即可"], tips: "豆腐要轻轻煮，避免煮散" },
                            { name: "拌木耳", ingredients: "黑木耳100g、胡萝卜丝30g、香菜20g、蒜蓉、香醋、生抽、香油", steps: ["黑木耳用温水泡发，洗净撕小朵", "开水焯烫2分钟，过凉水沥干", "胡萝卜切丝，香菜切段", "所有食材放入碗中", "加调料拌匀即可"], tips: "木耳要充分泡发，焯水去异味" }
                        ]
                    }
                }
            },
            30: {
                title: "第30天",
                meals: {
                    breakfast: {
                        title: "八宝粥 + 水煮蛋",
                        dishes: [
                            { name: "八宝粥", ingredients: "八宝粥米50g、清水500ml", steps: ["八宝粥米提前浸泡2小时", "锅中加水烧开，倒入八宝粥米", "大火煮开后转小火，煮45分钟", "煮至粥稠米烂即可"], tips: "八宝粥米要充分泡发，小火慢煮" },
                            { name: "水煮蛋", ingredients: "鸡蛋1个", steps: ["锅中加水烧开", "轻轻放入鸡蛋", "煮8分钟后捞出", "过凉水剥壳即可"], tips: "煮8分钟蛋黄刚好凝固，营养最佳" }
                        ]
                    },
                    lunch: {
                        title: "鸡蛋挂面",
                        dishes: [
                            { name: "鸡蛋挂面", ingredients: "挂面100g、鸡蛋1个、青菜100g、紫菜5g、盐、香油", steps: ["锅中加水500ml烧开", "下入挂面煮2分钟", "打入鸡蛋，不要搅动，煮1分钟", "加入青菜和紫菜煮1分钟", "用盐调味，滴香油即可"], tips: "鸡蛋下锅后不要立即搅动，让蛋白凝固" }
                        ]
                    },
                    dinner: {
                        title: "银耳汤 + 拌豆腐丝 + 小米粥",
                        dishes: [
                            { name: "银耳汤", ingredients: "银耳1朵、红枣5颗、桂圆8颗、冰糖适量", steps: ["银耳用温水泡发2小时，撕成小朵", "红枣去核，桂圆去壳", "锅中加水600ml，放入银耳", "大火煮开后转小火，煮30分钟", "加入红枣和桂圆煮15分钟", "最后加冰糖煮5分钟即可"], tips: "银耳要充分泡发，小火慢炖出胶质" },
                            { name: "拌豆腐丝", ingredients: "豆腐丝200g、胡萝卜丝50g、香菜30g、生抽、香醋、香油", steps: ["豆腐丝用开水烫1分钟，过凉水", "胡萝卜切丝，香菜切段", "将所有食材放入大碗", "加生抽、香醋、香油拌匀即可"], tips: "豆腐丝烫水去豆腥味，过凉水保持爽脆" }
                        ]
                    }
                }
            }
        };

        // 将所有详细菜谱数据合并到主数据中
        Object.assign(recipeData, detailedRecipes);
        Object.assign(recipeData, moreDetailedRecipes);
        Object.assign(recipeData, finalDetailedRecipes1);
        Object.assign(recipeData, finalDetailedRecipes2);

        function showSection(sectionId) {
            // 隐藏所有内容区域
            const sections = document.querySelectorAll('.content-section');
            sections.forEach(section => section.classList.remove('active'));

            // 显示选中的区域
            document.getElementById(sectionId).classList.add('active');

            // 更新导航按钮状态
            const buttons = document.querySelectorAll('.nav-btn');
            buttons.forEach(btn => btn.classList.remove('active'));
            event.target.classList.add('active');

            // 隐藏菜谱详情
            document.getElementById('recipe-detail').classList.remove('active');

            // 如果是完整菜谱区域，生成菜谱卡片
            if (sectionId === 'all-days') {
                generateAllDayCards();
            }
        }

        // 当前视图模式
        let currentView = 'compact';

        function generateAllDayCards() {
            const grid = document.getElementById('all-days-grid');

            if (currentView === 'weekly') {
                generateWeeklyView(grid);
            } else {
                generateDayCards(grid);
            }
        }

        function generateDayCards(grid) {
            let html = '';

            for (let day = 1; day <= 30; day++) {
                const dayData = recipeData[day];
                if (dayData) {
                    // 获取营养亮点
                    const nutritionTags = getNutritionHighlights(dayData);

                    if (currentView === 'compact') {
                        html += generateCompactCard(day, dayData, nutritionTags);
                    } else {
                        html += generateDetailedCard(day, dayData, nutritionTags);
                    }
                }
            }

            grid.innerHTML = html;
        }

        function generateCompactCard(day, dayData, nutritionTags) {
            return `
                <div class="day-card" onclick="showRecipeDetail(${day})">
                    <div class="day-card-header">
                        <h3>${dayData.title}</h3>
                        <div class="day-card-quick-info">第${Math.ceil(day/7)}周</div>
                    </div>
                    <div class="day-card-content">
                        <div class="meal-item">
                            <div class="meal-header">
                                <span class="meal-icon">🌅</span>
                                <div class="meal-title">早餐</div>
                            </div>
                            <div class="meal-content">${dayData.meals.breakfast.title}</div>
                        </div>
                        <div class="meal-item">
                            <div class="meal-header">
                                <span class="meal-icon">🌞</span>
                                <div class="meal-title">午餐</div>
                            </div>
                            <div class="meal-content">${dayData.meals.lunch.title}</div>
                        </div>
                        <div class="meal-item">
                            <div class="meal-header">
                                <span class="meal-icon">🌙</span>
                                <div class="meal-title">晚餐</div>
                            </div>
                            <div class="meal-content">${dayData.meals.dinner.title}</div>
                        </div>
                        <div class="nutrition-highlights">
                            ${nutritionTags.map(tag => `<span class="nutrition-tag-small">${tag}</span>`).join('')}
                        </div>
                    </div>
                </div>
            `;
        }

        function generateDetailedCard(day, dayData, nutritionTags) {
            return `
                <div class="day-card" onclick="showRecipeDetail(${day})">
                    <div class="day-card-header">
                        <h3>${dayData.title}</h3>
                        <div class="day-card-quick-info">第${Math.ceil(day/7)}周</div>
                    </div>
                    <div class="day-card-content">
                        <div class="meal-item">
                            <div class="meal-header">
                                <span class="meal-icon">🌅</span>
                                <div class="meal-title">早餐</div>
                            </div>
                            <div class="meal-content">${dayData.meals.breakfast.title}</div>
                            <img src="${getDishImage(dayData.meals.breakfast.dishes[0].name)}" alt="早餐" class="meal-image" loading="lazy" onerror="handleImageError(this, '早餐')">
                        </div>
                        <div class="meal-item">
                            <div class="meal-header">
                                <span class="meal-icon">🌞</span>
                                <div class="meal-title">午餐</div>
                            </div>
                            <div class="meal-content">${dayData.meals.lunch.title}</div>
                            <img src="${getDishImage(dayData.meals.lunch.dishes[0].name)}" alt="午餐" class="meal-image" loading="lazy" onerror="handleImageError(this, '午餐')">
                        </div>
                        <div class="meal-item">
                            <div class="meal-header">
                                <span class="meal-icon">🌙</span>
                                <div class="meal-title">晚餐</div>
                            </div>
                            <div class="meal-content">${dayData.meals.dinner.title}</div>
                            <img src="${getDishImage(dayData.meals.dinner.dishes[0].name)}" alt="晚餐" class="meal-image" loading="lazy" onerror="handleImageError(this, '晚餐')">
                        </div>
                        <div class="nutrition-highlights">
                            ${nutritionTags.map(tag => `<span class="nutrition-tag-small">${tag}</span>`).join('')}
                        </div>
                    </div>
                </div>
            `;
        }

        function generateWeeklyView(grid) {
            let html = '';

            for (let week = 1; week <= 5; week++) {
                const startDay = (week - 1) * 7 + 1;
                const endDay = Math.min(week * 7, 30);

                html += `
                    <div class="week-container" style="grid-column: 1 / -1; margin-bottom: 30px;">
                        <h3 style="color: #667eea; margin-bottom: 20px; text-align: center;">第${week}周 (第${startDay}-${endDay}天)</h3>
                        <div class="week-grid" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px;">
                `;

                for (let day = startDay; day <= endDay; day++) {
                    const dayData = recipeData[day];
                    if (dayData) {
                        html += `
                            <div class="week-day-card" onclick="showRecipeDetail(${day})" style="background: white; border: 1px solid #e9ecef; border-radius: 10px; padding: 15px; cursor: pointer; transition: all 0.3s ease;">
                                <h4 style="color: #667eea; margin: 0 0 10px 0; font-size: 1rem;">${dayData.title}</h4>
                                <div style="font-size: 0.8rem; color: #666; line-height: 1.4;">
                                    <div>🌅 ${dayData.meals.breakfast.title}</div>
                                    <div>🌞 ${dayData.meals.lunch.title}</div>
                                    <div>🌙 ${dayData.meals.dinner.title}</div>
                                </div>
                            </div>
                        `;
                    }
                }

                html += `
                        </div>
                    </div>
                `;
            }

            grid.innerHTML = html;

            // 为周视图添加悬停效果
            setTimeout(() => {
                const weekCards = document.querySelectorAll('.week-day-card');
                weekCards.forEach(card => {
                    card.addEventListener('mouseenter', function() {
                        this.style.transform = 'translateY(-2px)';
                        this.style.boxShadow = '0 5px 15px rgba(0,0,0,0.1)';
                        this.style.borderColor = '#667eea';
                    });
                    card.addEventListener('mouseleave', function() {
                        this.style.transform = 'translateY(0)';
                        this.style.boxShadow = 'none';
                        this.style.borderColor = '#e9ecef';
                    });
                });
            }, 100);
        }

        function getNutritionHighlights(dayData) {
            const highlights = new Set();

            // 分析所有餐次的菜品
            Object.values(dayData.meals).forEach(meal => {
                meal.dishes.forEach(dish => {
                    const tags = getNutritionTags(dish.name);
                    tags.forEach(tag => highlights.add(tag.replace(/🥚|🥬|🦐|🦴|🌰|🩸|🌾|🌟/, '').trim()));
                });
            });

            return Array.from(highlights).slice(0, 3); // 最多显示3个标签
        }

        function switchView(viewType) {
            currentView = viewType;

            // 更新按钮状态
            const buttons = document.querySelectorAll('.view-btn');
            buttons.forEach(btn => btn.classList.remove('active'));

            // 找到被点击的按钮并设为活跃状态
            const clickedButton = Array.from(buttons).find(btn =>
                btn.textContent.includes(viewType === 'compact' ? '紧凑' :
                                       viewType === 'detailed' ? '详细' : '周视图')
            );
            if (clickedButton) {
                clickedButton.classList.add('active');
            }

            // 重新生成卡片
            generateAllDayCards();

            // 根据视图类型调整网格布局
            const grid = document.getElementById('all-days-grid');
            grid.className = 'day-grid'; // 重置类名

            if (viewType === 'weekly') {
                grid.style.gridTemplateColumns = '1fr';
                grid.classList.add('weekly-view');
            } else if (viewType === 'compact') {
                grid.style.gridTemplateColumns = 'repeat(auto-fill, minmax(280px, 1fr))';
                grid.classList.add('compact-view');
            } else {
                grid.style.gridTemplateColumns = 'repeat(auto-fill, minmax(320px, 1fr))';
                grid.classList.add('detailed-view');
            }
        }

        function showRecipeDetail(day) {
            const dayData = recipeData[day];
            if (!dayData) return;

            // 显示菜谱详情
            document.getElementById('recipe-detail').classList.add('active');

            // 隐藏其他内容区域
            const sections = document.querySelectorAll('.content-section');
            sections.forEach(section => section.classList.remove('active'));

            // 生成详细内容
            const content = document.getElementById('recipe-content');
            let html = `
                <div class="recipe-header">
                    <h2>${dayData.title}菜谱详情</h2>
                </div>
            `;

            // 生成三餐内容
            const mealIcons = { breakfast: '🌅', lunch: '🌞', dinner: '🌙' };
            const mealNames = { breakfast: '早餐', lunch: '午餐', dinner: '晚餐' };

            for (const [mealType, mealData] of Object.entries(dayData.meals)) {
                html += `
                    <div class="meal-section">
                        <h3>${mealIcons[mealType]} ${mealNames[mealType]}：${mealData.title}</h3>
                `;

                for (const dish of mealData.dishes) {
                    const dishImage = getDishImage(dish.name);

                    // 生成营养标签
                    const nutritionTags = getNutritionTags(dish.name);

                    // 估算烹饪时间
                    const cookingTime = estimateCookingTime(dish.steps);

                    html += `
                        <div class="dish-item">
                            <div class="dish-header">
                                <img src="${dishImage}" alt="${dish.name}" class="dish-image" loading="lazy" onerror="handleDishImageError(this, '${dish.name}')">
                                <div class="dish-title-container">
                                    <div class="dish-title">${dish.name}</div>
                                    <div class="dish-nutrition-tags">
                                        ${nutritionTags.map(tag => `<span class="nutrition-tag">${tag}</span>`).join('')}
                                    </div>
                                </div>
                            </div>
                            <div class="dish-content">
                                <div class="content-section-dish cooking-time">
                                    <strong>⏱️ 烹饪时间</strong>
                                    <div class="time-info">
                                        <div class="time-item">
                                            <span class="time-value">${cookingTime.prep}</span>
                                            <span class="time-label">准备时间</span>
                                        </div>
                                        <div class="time-item">
                                            <span class="time-value">${cookingTime.cook}</span>
                                            <span class="time-label">烹饪时间</span>
                                        </div>
                                        <div class="time-item">
                                            <span class="time-value">${cookingTime.total}</span>
                                            <span class="time-label">总时间</span>
                                        </div>
                                    </div>
                                </div>

                                <div class="content-section-dish ingredients">
                                    <strong>🥘 食材清单</strong>
                                    <div class="ingredients-list">${dish.ingredients}</div>
                                </div>

                                <div class="content-section-dish steps">
                                    <strong>👩‍🍳 制作步骤</strong>
                                    <ol>
                                        ${dish.steps.map((step, index) => {
                                            const stepTime = getStepTime(step);
                                            return `<li>${step}${stepTime ? `<span class="step-time">${stepTime}</span>` : ''}</li>`;
                                        }).join('')}
                                    </ol>
                                </div>

                                <div class="content-section-dish tips">
                                    <strong>💡 烹饪技巧</strong>
                                    <div class="tips-content">${dish.tips}</div>
                                </div>
                            </div>
                        </div>
                    `;
                }

                html += '</div>';
            }

            content.innerHTML = html;
        }

        function hideRecipeDetail() {
            document.getElementById('recipe-detail').classList.remove('active');
            // 返回到完整菜谱区域
            document.getElementById('all-days').classList.add('active');
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 默认不生成菜谱卡片，等用户点击时再生成
        });
    </script>
</body>
</html>
